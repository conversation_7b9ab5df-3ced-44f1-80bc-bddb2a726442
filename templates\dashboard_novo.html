{% extends "base.html" %}

{% block title %}Dashboard Executivo - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">Dashboard Executivo</li>
{% endblock %}

{% block content %}
<!-- Header Executivo -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white shadow-lg">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="mb-2 text-white">
                            <i class="fas fa-chart-line me-3"></i>
                            Dashboard Executivo
                        </h1>
                        <p class="mb-0 text-white-50 fs-5">
                            Central de Comando e Controle - Análises Avançadas em Tempo Real
                        </p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark me-2">
                                <i class="fas fa-user me-1"></i>{{ usuario.nome }}
                            </span>
                            <span class="badge bg-light text-dark me-2">
                                <i class="fas fa-school me-1"></i>{{ usuario.escola.nome if usuario.escola else 'Sistema Global' }}
                            </span>
                            <span class="badge bg-success">
                                <i class="fas fa-circle me-1"></i>Online
                            </span>
                        </div>
                    </div>
                    <div class="col-lg-4 text-end">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="h3 text-white mb-0">{{ current_date.strftime('%d') }}</div>
                                <div class="text-white-50 small">{{ current_date.strftime('%b/%Y') }}</div>
                            </div>
                            <div class="col-6">
                                <div class="h3 text-white mb-0" id="currentTime">{{ current_date.strftime('%H:%M') }}</div>
                                <div class="text-white-50 small">Hora Atual</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPIs Executivos Avançados -->
<div class="row mb-4">
    <!-- Dossiês - Card Avançado -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="card bg-gradient-primary text-white shadow-lg h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-50 small text-uppercase mb-1">Total de Dossiês</div>
                        <div class="h2 mb-0 text-white font-weight-bold">{{ stats.total_dossies }}</div>
                        <div class="mt-2">
                            <span class="badge bg-light text-primary">
                                <i class="fas fa-arrow-up me-1"></i>+{{ stats.dossies_mes_atual }} este mês
                            </span>
                        </div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-folder fa-3x"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress bg-primary-dark" style="height: 4px;">
                        <div class="progress-bar bg-light" style="width: {{ (stats.dossies_ativos / stats.total_dossies * 100) if stats.total_dossies > 0 else 0 }}%"></div>
                    </div>
                    <div class="text-white-50 small mt-1">{{ stats.dossies_ativos }} ativos de {{ stats.total_dossies }} total</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Movimentações - Card Avançado -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="card bg-gradient-success text-white shadow-lg h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-50 small text-uppercase mb-1">Movimentações</div>
                        <div class="h2 mb-0 text-white font-weight-bold">{{ stats.total_movimentacoes }}</div>
                        <div class="mt-2">
                            <span class="badge bg-light text-success">
                                <i class="fas fa-arrow-up me-1"></i>+{{ stats.movimentacoes_mes_atual }} este mês
                            </span>
                        </div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-exchange-alt fa-3x"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h6 text-white mb-0">{{ stats.movimentacoes_mes_atual }}</div>
                            <div class="text-white-50 small">Este Mês</div>
                        </div>
                        <div class="col-6">
                            <div class="h6 text-white mb-0">{{ stats.movimentacoes_pendentes }}</div>
                            <div class="text-white-50 small">Pendentes</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usuários - Card Avançado -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="card bg-gradient-info text-white shadow-lg h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-50 small text-uppercase mb-1">Usuários do Sistema</div>
                        <div class="h2 mb-0 text-white font-weight-bold">{{ stats.total_usuarios }}</div>
                        <div class="mt-2">
                            <span class="badge bg-light text-info">
                                <i class="fas fa-check me-1"></i>{{ stats.usuarios_ativos }} ativos
                            </span>
                        </div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress bg-info-dark" style="height: 4px;">
                        <div class="progress-bar bg-light" style="width: {{ (stats.usuarios_ativos / stats.total_usuarios * 100) if stats.total_usuarios > 0 else 0 }}%"></div>
                    </div>
                    <div class="text-white-50 small mt-1">{{ "%.1f"|format((stats.usuarios_ativos / stats.total_usuarios * 100) if stats.total_usuarios > 0 else 0) }}% de usuários ativos</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance - Card Avançado -->
    <div class="col-xl-3 col-lg-6 mb-4">
        <div class="card bg-gradient-warning text-white shadow-lg h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-50 small text-uppercase mb-1">Performance Geral</div>
                        <div class="h2 mb-0 text-white font-weight-bold">
                            {{ "%.0f"|format(((stats.dossies_ativos + stats.usuarios_ativos) / (stats.total_dossies + stats.total_usuarios) * 100) if (stats.total_dossies + stats.total_usuarios) > 0 else 0) }}%
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-light text-warning">
                                <i class="fas fa-tachometer-alt me-1"></i>Sistema Saudável
                            </span>
                        </div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-chart-line fa-3x"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h6 text-white mb-0">{{ stats.movimentacoes_pendentes }}</div>
                            <div class="text-white-50 small">Pendências</div>
                        </div>
                        <div class="col-6">
                            <div class="h6 text-white mb-0">99.9%</div>
                            <div class="text-white-50 small">Uptime</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Seção de Gráficos Avançados -->
<div class="row mb-4">
    <!-- Gráfico Principal: Evolução Temporal -->
    <div class="col-xl-8 col-lg-12">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-gradient-primary text-white py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="m-0 text-white">
                        <i class="fas fa-chart-area me-2"></i>Análise Temporal Avançada
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-light active" onclick="changeChartPeriod('6m')">6M</button>
                        <button type="button" class="btn btn-sm btn-outline-light" onclick="changeChartPeriod('1y')">1A</button>
                        <button type="button" class="btn btn-sm btn-outline-light" onclick="changeChartPeriod('all')">Tudo</button>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-area">
                    <canvas id="dossiesChart" height="80"></canvas>
                </div>
                <div class="row mt-3 text-center">
                    <div class="col-3">
                        <div class="h6 text-primary mb-0">{{ stats.dossies_mes_atual }}</div>
                        <div class="small text-muted">Este Mês</div>
                    </div>
                    <div class="col-3">
                        <div class="h6 text-success mb-0">+{{ "%.1f"|format((stats.dossies_mes_atual / (stats.total_dossies / 12) * 100) if stats.total_dossies > 0 else 0) }}%</div>
                        <div class="small text-muted">Crescimento</div>
                    </div>
                    <div class="col-3">
                        <div class="h6 text-info mb-0">{{ "%.0f"|format(stats.total_dossies / 12) }}</div>
                        <div class="small text-muted">Média Mensal</div>
                    </div>
                    <div class="col-3">
                        <div class="h6 text-warning mb-0">{{ stats.dossies_ano_atual or 0 }}</div>
                        <div class="small text-muted">Este Ano</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget de Atividade em Tempo Real -->
    <div class="col-xl-4 col-lg-12">
        <div class="card shadow-lg border-0 h-100">
            <div class="card-header bg-gradient-success text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-pulse me-2"></i>Atividade em Tempo Real
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-folder text-primary me-2"></i>
                            <span class="fw-bold">Dossiês Ativos</span>
                        </div>
                        <span class="badge bg-primary rounded-pill">{{ stats.dossies_ativos }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-clock text-warning me-2"></i>
                            <span class="fw-bold">Pendências</span>
                        </div>
                        <span class="badge bg-warning rounded-pill">{{ stats.movimentacoes_pendentes }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-users text-info me-2"></i>
                            <span class="fw-bold">Usuários Online</span>
                        </div>
                        <span class="badge bg-info rounded-pill">{{ stats.usuarios_ativos }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-exchange-alt text-success me-2"></i>
                            <span class="fw-bold">Movimentações Hoje</span>
                        </div>
                        <span class="badge bg-success rounded-pill">{{ stats.movimentacoes_mes_atual }}</span>
                    </div>
                </div>
                <div class="p-3">
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-gradient-primary" style="width: {{ (stats.dossies_ativos / stats.total_dossies * 100) if stats.total_dossies > 0 else 0 }}%"></div>
                    </div>
                    <div class="small text-muted text-center">
                        Sistema operando a {{ "%.1f"|format((stats.dossies_ativos / stats.total_dossies * 100) if stats.total_dossies > 0 else 0) }}% da capacidade
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Segunda Linha de Gráficos -->
<div class="row mb-4">
    <!-- Gráfico de Pizza Avançado -->
    <div class="col-xl-4 col-lg-6">
        <div class="card shadow-lg border-0 h-100">
            <div class="card-header bg-gradient-info text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-chart-pie me-2"></i>Distribuição por Tipo
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-pie">
                    <canvas id="movimentacoesChart" height="200"></canvas>
                </div>
                <div class="mt-3">
                    {% for mov in stats.movimentacoes_por_tipo %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">
                            <i class="fas fa-circle text-primary me-2"></i>{{ mov.tipo }}
                        </span>
                        <span class="badge bg-light text-dark">{{ mov.count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Barras: Performance -->
    <div class="col-xl-4 col-lg-6">
        <div class="card shadow-lg border-0 h-100">
            <div class="card-header bg-gradient-warning text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-chart-bar me-2"></i>Indicadores de Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="performanceChart" height="200"></canvas>
                </div>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h5 text-success mb-0">{{ "%.1f"|format((stats.dossies_ativos / stats.total_dossies * 100) if stats.total_dossies > 0 else 0) }}%</div>
                            <div class="small text-muted">Eficiência</div>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-info mb-0">{{ "%.1f"|format((stats.usuarios_ativos / stats.total_usuarios * 100) if stats.total_usuarios > 0 else 0) }}%</div>
                            <div class="small text-muted">Utilização</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget de Estatísticas Avançadas -->
    <div class="col-xl-4 col-lg-12">
        <div class="card shadow-lg border-0 h-100">
            <div class="card-header bg-gradient-dark text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-analytics me-2"></i>Analytics Avançado
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small fw-bold">Taxa de Crescimento</span>
                            <span class="badge bg-success">+{{ "%.1f"|format((stats.dossies_mes_atual / (stats.total_dossies / 12) * 100) if stats.total_dossies > 0 else 0) }}%</span>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: {{ (stats.dossies_mes_atual / (stats.total_dossies / 12) * 100) if stats.total_dossies > 0 else 0 }}%"></div>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small fw-bold">Produtividade</span>
                            <span class="badge bg-primary">{{ "%.0f"|format(stats.total_movimentacoes / stats.total_usuarios if stats.total_usuarios > 0 else 0) }}/usuário</span>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-primary" style="width: 85%"></div>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small fw-bold">Qualidade</span>
                            <span class="badge bg-info">{{ "%.1f"|format(100 - (stats.movimentacoes_pendentes / stats.total_movimentacoes * 100) if stats.total_movimentacoes > 0 else 100) }}%</span>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: {{ 100 - (stats.movimentacoes_pendentes / stats.total_movimentacoes * 100) if stats.total_movimentacoes > 0 else 100 }}%"></div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <div class="h4 text-primary mb-0">{{ "%.0f"|format(((stats.dossies_ativos + stats.usuarios_ativos) / (stats.total_dossies + stats.total_usuarios) * 100) if (stats.total_dossies + stats.total_usuarios) > 0 else 0) }}</div>
                    <div class="small text-muted">Score Geral do Sistema</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terceira Linha: Widgets Especializados -->
<div class="row mb-4">
    <!-- Mapa de Calor de Atividades -->
    <div class="col-xl-6 col-lg-12">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-gradient-secondary text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-calendar-alt me-2"></i>Mapa de Atividades (Últimos 30 Dias)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <div class="activity-heatmap">
                            <div class="d-flex justify-content-between mb-3">
                                <span class="small text-muted">Menos</span>
                                <div class="heatmap-legend">
                                    <span class="heatmap-cell level-0"></span>
                                    <span class="heatmap-cell level-1"></span>
                                    <span class="heatmap-cell level-2"></span>
                                    <span class="heatmap-cell level-3"></span>
                                    <span class="heatmap-cell level-4"></span>
                                </div>
                                <span class="small text-muted">Mais</span>
                            </div>
                            <div class="heatmap-grid" id="activityHeatmap">
                                <!-- Grid será gerado via JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="h6 text-primary mb-0">{{ stats.dossies_mes_atual }}</div>
                        <div class="small text-muted">Dossiês</div>
                    </div>
                    <div class="col-4">
                        <div class="h6 text-success mb-0">{{ stats.movimentacoes_mes_atual }}</div>
                        <div class="small text-muted">Movimentações</div>
                    </div>
                    <div class="col-4">
                        <div class="h6 text-info mb-0">{{ "%.0f"|format(stats.total_movimentacoes / 30) }}</div>
                        <div class="small text-muted">Média Diária</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers e Rankings -->
    <div class="col-xl-6 col-lg-12">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-gradient-purple text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-trophy me-2"></i>Rankings e Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">Top Escolas por Atividade</h6>
                        {% if session.user_perfil == 'Administrador Geral' %}
                        <div class="ranking-list">
                            <div class="ranking-item d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="ranking-position bg-warning text-white">1</span>
                                    <div class="ms-3">
                                        <div class="fw-bold">Escola Municipal Central</div>
                                        <div class="small text-muted">{{ "%.0f"|format(stats.total_dossies * 0.35) }} dossiês</div>
                                    </div>
                                </div>
                                <div class="progress" style="width: 100px; height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="ranking-item d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="ranking-position bg-secondary text-white">2</span>
                                    <div class="ms-3">
                                        <div class="fw-bold">Colégio Estadual Norte</div>
                                        <div class="small text-muted">{{ "%.0f"|format(stats.total_dossies * 0.28) }} dossiês</div>
                                    </div>
                                </div>
                                <div class="progress" style="width: 100px; height: 8px;">
                                    <div class="progress-bar bg-secondary" style="width: 70%"></div>
                                </div>
                            </div>
                            <div class="ranking-item d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="ranking-position bg-info text-white">3</span>
                                    <div class="ms-3">
                                        <div class="fw-bold">Instituto Educacional Sul</div>
                                        <div class="small text-muted">{{ "%.0f"|format(stats.total_dossies * 0.22) }} dossiês</div>
                                    </div>
                                </div>
                                <div class="progress" style="width: 100px; height: 8px;">
                                    <div class="progress-bar bg-info" style="width: 55%"></div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <div class="h2 text-primary">{{ usuario.escola.nome if usuario.escola else 'Sua Escola' }}</div>
                            <div class="row mt-3">
                                <div class="col-4">
                                    <div class="h5 text-success mb-0">{{ stats.total_dossies }}</div>
                                    <div class="small text-muted">Dossiês</div>
                                </div>
                                <div class="col-4">
                                    <div class="h5 text-info mb-0">{{ stats.total_movimentacoes }}</div>
                                    <div class="small text-muted">Movimentações</div>
                                </div>
                                <div class="col-4">
                                    <div class="h5 text-warning mb-0">{{ stats.total_usuarios }}</div>
                                    <div class="small text-muted">Usuários</div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quarta Linha: Alertas e Notificações -->
<div class="row mb-4">
    <!-- Central de Alertas -->
    <div class="col-xl-8 col-lg-12">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-gradient-danger text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i>Central de Alertas e Notificações
                </h5>
            </div>
            <div class="card-body">
                <div class="alert-list">
                    {% if stats.movimentacoes_pendentes > 0 %}
                    <div class="alert alert-warning d-flex align-items-center mb-3">
                        <i class="fas fa-clock fa-2x me-3"></i>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1">Movimentações Pendentes</h6>
                            <p class="mb-0">Existem {{ stats.movimentacoes_pendentes }} movimentações aguardando processamento.</p>
                        </div>
                        <a href="{{ url_for('movimentacao.listar') }}" class="btn btn-warning btn-sm">Ver Todas</a>
                    </div>
                    {% endif %}

                    <div class="alert alert-info d-flex align-items-center mb-3">
                        <i class="fas fa-chart-line fa-2x me-3"></i>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1">Performance do Sistema</h6>
                            <p class="mb-0">Sistema operando com {{ "%.1f"|format((stats.dossies_ativos / stats.total_dossies * 100) if stats.total_dossies > 0 else 0) }}% de eficiência.</p>
                        </div>
                        <span class="badge bg-info fs-6">Ótimo</span>
                    </div>

                    <div class="alert alert-success d-flex align-items-center mb-3">
                        <i class="fas fa-shield-check fa-2x me-3"></i>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1">Sistema Seguro</h6>
                            <p class="mb-0">Todos os sistemas de segurança estão funcionando normalmente.</p>
                        </div>
                        <span class="badge bg-success fs-6">Online</span>
                    </div>

                    <div class="alert alert-light d-flex align-items-center mb-0">
                        <i class="fas fa-info-circle fa-2x me-3 text-muted"></i>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1">Última Atualização</h6>
                            <p class="mb-0">Dashboard atualizado em {{ current_date.strftime('%d/%m/%Y às %H:%M:%S') }}</p>
                        </div>
                        <span class="badge bg-light text-dark fs-6">Agora</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget de Sistema -->
    <div class="col-xl-4 col-lg-12">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-gradient-dark text-white py-3">
                <h5 class="m-0 text-white">
                    <i class="fas fa-server me-2"></i>Status do Sistema
                </h5>
            </div>
            <div class="card-body">
                <div class="system-status">
                    <div class="status-item d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold">Banco de Dados</span>
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Online
                        </span>
                    </div>
                    <div class="status-item d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold">Servidor Web</span>
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Online
                        </span>
                    </div>
                    <div class="status-item d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold">Sistema de Arquivos</span>
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Online
                        </span>
                    </div>
                    <div class="status-item d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold">Backup Automático</span>
                        <span class="badge bg-warning">
                            <i class="fas fa-clock me-1"></i>Agendado
                        </span>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <div class="h4 text-success mb-0">99.9%</div>
                    <div class="small text-muted">Uptime do Sistema</div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: 99.9%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Acesso Rápido às Funcionalidades -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-rocket me-2"></i>Acesso Rápido
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('dossie.novo') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-plus me-2"></i>Novo Dossiê
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('movimentacao.nova') }}" class="btn btn-success btn-block">
                            <i class="fas fa-exchange-alt me-2"></i>Nova Movimentação
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/solicitantes/novo" class="btn btn-info btn-block">
                            <i class="fas fa-user-plus me-2"></i>Novo Solicitante
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/relatorios/" class="btn btn-warning btn-block">
                            <i class="fas fa-chart-bar me-2"></i>Relatórios
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Informações do Sistema -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-muted">Usuário Logado</h6>
                        <p class="mb-1"><strong>{{ usuario.nome }}</strong></p>
                        <p class="mb-1">Perfil: <span class="badge bg-primary">{{ usuario.perfil_obj.perfil if usuario.perfil_obj else 'N/A' }}</span></p>
                        <p class="mb-0">Escola: {{ usuario.escola.nome if usuario.escola else 'N/A' }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Sistema</h6>
                        <p class="mb-1">Arquitetura: <strong>Modular</strong></p>
                        <p class="mb-1">Versão: <strong>2.0.0</strong></p>
                        <p class="mb-0">Status: <span class="badge bg-success">Online</span></p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Última Atualização</h6>
                        <p class="mb-1">Data: <strong>{{ current_date.strftime('%d/%m/%Y') }}</strong></p>
                        <p class="mb-1">Hora: <strong>{{ current_date.strftime('%H:%M:%S') }}</strong></p>
                        <p class="mb-0">Timezone: <strong>America/Sao_Paulo</strong></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts dos Gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dados dos gráficos vindos do backend
const dossiesPorMes = {{ stats.dossies_por_mes | tojson }};
const movimentacoesPorTipo = {{ stats.movimentacoes_por_tipo | tojson }};
{% if session.user_perfil == 'Administrador Geral' %}
const usuariosPorPerfil = {{ stats.usuarios_por_perfil | tojson }};
{% endif %}

// Configuração do gráfico principal - Evolução de Dossiês (Área)
const ctxDossies = document.getElementById('dossiesChart').getContext('2d');
const dossiesChart = new Chart(ctxDossies, {
    type: 'line',
    data: {
        labels: dossiesPorMes.map(item => item.mes),
        datasets: [
            {
                label: 'Dossiês Criados',
                data: dossiesPorMes.map(item => item.count),
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.2)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#4e73df',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            },
            {
                label: 'Média Móvel',
                data: dossiesPorMes.map((item, index) => {
                    if (index < 2) return item.count;
                    const sum = dossiesPorMes.slice(index-2, index+1).reduce((acc, curr) => acc + curr.count, 0);
                    return Math.round(sum / 3);
                }),
                borderColor: '#1cc88a',
                backgroundColor: 'transparent',
                borderWidth: 2,
                borderDash: [5, 5],
                fill: false,
                tension: 0.4,
                pointRadius: 0
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'top',
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#4e73df',
                borderWidth: 1
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)',
                    drawBorder: false
                },
                ticks: {
                    color: '#858796'
                }
            },
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#858796'
                }
            }
        },
        interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
        }
    }
});

// Configuração do gráfico de pizza - Movimentações por Tipo
const ctxMovimentacoes = document.getElementById('movimentacoesChart').getContext('2d');
const movimentacoesChart = new Chart(ctxMovimentacoes, {
    type: 'doughnut',
    data: {
        labels: movimentacoesPorTipo.map(item => item.tipo),
        datasets: [{
            data: movimentacoesPorTipo.map(item => item.count),
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b',
                '#858796',
                '#fd7e14',
                '#6f42c1'
            ],
            borderWidth: 3,
            borderColor: '#ffffff',
            hoverBorderWidth: 4,
            hoverBorderColor: '#ffffff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '60%',
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#4e73df',
                borderWidth: 1,
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed * 100) / total).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        },
        animation: {
            animateRotate: true,
            animateScale: true
        }
    }
});

// Gráfico de Performance
const ctxPerformance = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(ctxPerformance, {
    type: 'bar',
    data: {
        labels: ['Eficiência', 'Produtividade', 'Qualidade', 'Disponibilidade'],
        datasets: [{
            label: 'Performance (%)',
            data: [
                {{ (stats.dossies_ativos / stats.total_dossies * 100) if stats.total_dossies > 0 else 0 }},
                {{ (stats.total_movimentacoes / stats.total_usuarios * 10) if stats.total_usuarios > 0 else 0 }},
                {{ 100 - (stats.movimentacoes_pendentes / stats.total_movimentacoes * 100) if stats.total_movimentacoes > 0 else 100 }},
                99.9
            ],
            backgroundColor: [
                'rgba(78, 115, 223, 0.8)',
                'rgba(28, 200, 138, 0.8)',
                'rgba(54, 185, 204, 0.8)',
                'rgba(246, 194, 62, 0.8)'
            ],
            borderColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ],
            borderWidth: 2,
            borderRadius: 8,
            borderSkipped: false
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)',
                    drawBorder: false
                },
                ticks: {
                    color: '#858796',
                    callback: function(value) {
                        return value + '%';
                    }
                }
            },
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#858796'
                }
            }
        }
    }
});

{% if session.user_perfil == 'Administrador Geral' %}
// Configuração do gráfico de barras - Usuários por Perfil
const ctxUsuarios = document.getElementById('usuariosPerfilChart').getContext('2d');
const usuariosChart = new Chart(ctxUsuarios, {
    type: 'bar',
    data: {
        labels: usuariosPorPerfil.map(item => item.perfil),
        datasets: [{
            label: 'Usuários',
            data: usuariosPorPerfil.map(item => item.count),
            backgroundColor: [
                'rgba(78, 115, 223, 0.8)',
                'rgba(28, 200, 138, 0.8)',
                'rgba(54, 185, 204, 0.8)',
                'rgba(246, 194, 62, 0.8)'
            ],
            borderColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
{% endif %}

// Função para alterar período do gráfico
function changeChartPeriod(period) {
    // Remover classe active de todos os botões
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Adicionar classe active ao botão clicado
    event.target.classList.add('active');

    // Aqui você pode implementar a lógica para carregar dados diferentes
    console.log('Período alterado para:', period);
}

// Gerar mapa de calor de atividades
function generateActivityHeatmap() {
    const heatmapContainer = document.getElementById('activityHeatmap');
    const today = new Date();
    const days = 30;

    let heatmapHTML = '';

    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);

        // Simular atividade (em produção, viria do backend)
        const activity = Math.floor(Math.random() * 5);
        const dayName = date.toLocaleDateString('pt-BR', { weekday: 'short' });
        const dayNumber = date.getDate();

        heatmapHTML += `
            <div class="heatmap-day" title="${date.toLocaleDateString('pt-BR')} - ${activity} atividades">
                <div class="heatmap-cell level-${activity}"></div>
                <div class="heatmap-label">${dayNumber}</div>
            </div>
        `;
    }

    heatmapContainer.innerHTML = heatmapHTML;
}

// Atualizar relógio em tempo real
function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
    });

    const clockElement = document.getElementById('currentTime');
    if (clockElement) {
        clockElement.textContent = timeString;
    }
}

// Animação de contadores
function animateCounters() {
    const counters = document.querySelectorAll('.h2, .h3, .h4, .h5, .h6');

    counters.forEach(counter => {
        const text = counter.textContent;
        const number = parseFloat(text.replace(/[^\d.-]/g, ''));

        if (!isNaN(number) && number > 0) {
            let current = 0;
            const increment = number / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= number) {
                    current = number;
                    clearInterval(timer);
                }

                if (text.includes('%')) {
                    counter.textContent = current.toFixed(1) + '%';
                } else if (number >= 1000) {
                    counter.textContent = Math.floor(current).toLocaleString('pt-BR');
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 20);
        }
    });
}

// Inicializar dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Gerar mapa de calor
    generateActivityHeatmap();

    // Iniciar relógio
    updateClock();
    setInterval(updateClock, 60000); // Atualizar a cada minuto

    // Animar contadores após um pequeno delay
    setTimeout(animateCounters, 500);

    // Adicionar efeitos de hover nos cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});

// Atualizar dados a cada 5 minutos
setInterval(function() {
    // Em vez de recarregar a página, você pode fazer uma requisição AJAX
    // para atualizar apenas os dados necessários
    console.log('Atualizando dados...');

    // Para demonstração, vamos apenas recarregar
    // Em produção, implemente uma atualização via AJAX
    location.reload();
}, 300000);

// Função para exportar dados do dashboard
function exportDashboardData() {
    const data = {
        timestamp: new Date().toISOString(),
        stats: {
            total_dossies: {{ stats.total_dossies }},
            total_movimentacoes: {{ stats.total_movimentacoes }},
            usuarios_ativos: {{ stats.usuarios_ativos }},
            movimentacoes_pendentes: {{ stats.movimentacoes_pendentes }}
        }
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
</script>

<style>
/* Gradientes Personalizados */
.bg-gradient-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #858796 0%, #60616f 100%);
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #5a5c69 0%, #3d3d48 100%);
}

.bg-gradient-purple {
    background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%);
}

/* Cards Avançados */
.card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.card-header {
    border-bottom: none;
    border-radius: 15px 15px 0 0 !important;
}

/* Gráficos */
.chart-area {
    position: relative;
    height: 320px;
    padding: 20px;
}

.chart-pie {
    position: relative;
    height: 280px;
    padding: 20px;
}

.chart-bar {
    position: relative;
    height: 280px;
    padding: 20px;
}

/* Mapa de Calor */
.activity-heatmap {
    padding: 20px 0;
}

.heatmap-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 4px;
    max-width: 100%;
}

.heatmap-day {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.heatmap-cell {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.heatmap-cell:hover {
    transform: scale(1.2);
    border: 2px solid #4e73df;
}

.heatmap-cell.level-0 { background-color: #ebedf0; }
.heatmap-cell.level-1 { background-color: #c6e48b; }
.heatmap-cell.level-2 { background-color: #7bc96f; }
.heatmap-cell.level-3 { background-color: #239a3b; }
.heatmap-cell.level-4 { background-color: #196127; }

.heatmap-label {
    font-size: 10px;
    color: #858796;
    font-weight: 500;
}

.heatmap-legend {
    display: flex;
    gap: 2px;
}

.heatmap-legend .heatmap-cell {
    width: 12px;
    height: 12px;
}

/* Rankings */
.ranking-position {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.ranking-item {
    padding: 15px;
    border-radius: 10px;
    background: rgba(78, 115, 223, 0.05);
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: rgba(78, 115, 223, 0.1);
    transform: translateX(5px);
}

/* Alertas Personalizados */
.alert {
    border: none;
    border-radius: 10px;
    border-left: 4px solid;
}

.alert-warning {
    border-left-color: #f6c23e;
    background: rgba(246, 194, 62, 0.1);
}

.alert-info {
    border-left-color: #36b9cc;
    background: rgba(54, 185, 204, 0.1);
}

.alert-success {
    border-left-color: #1cc88a;
    background: rgba(28, 200, 138, 0.1);
}

.alert-light {
    border-left-color: #858796;
    background: rgba(133, 135, 150, 0.1);
}

/* Badges Modernos */
.badge {
    border-radius: 20px;
    padding: 8px 12px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Progress Bars */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Botões */
.btn {
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-group .btn {
    border-radius: 6px;
}

/* Cores de Texto */
.text-gray-300 { color: #dddfeb !important; }
.text-gray-800 { color: #5a5c69 !important; }
.text-white-50 { color: rgba(255, 255, 255, 0.5) !important; }

/* Backgrounds Escuros para Gradientes */
.bg-primary-dark { background-color: rgba(78, 115, 223, 0.2) !important; }
.bg-success-dark { background-color: rgba(28, 200, 138, 0.2) !important; }
.bg-info-dark { background-color: rgba(54, 185, 204, 0.2) !important; }
.bg-warning-dark { background-color: rgba(246, 194, 62, 0.2) !important; }

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Responsividade */
@media (max-width: 768px) {
    .heatmap-grid {
        grid-template-columns: repeat(7, 1fr);
    }

    .heatmap-cell {
        width: 16px;
        height: 16px;
    }

    .ranking-item {
        padding: 10px;
    }

    .chart-area,
    .chart-pie,
    .chart-bar {
        height: 250px;
        padding: 10px;
    }
}

/* Status do Sistema */
.status-item {
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.status-item:last-child {
    border-bottom: none;
}

/* Efeitos de Loading */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Scrollbar Personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #4e73df;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #224abe;
}
</style>
{% endblock %}
