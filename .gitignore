# .gitignore para Flask e Python
# Criado para o projeto Sistema de Dossiês Escolares

# ===================================
# PYTHON
# ===================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff (caso tenha arquivos Django misturados):
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
docs
# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===================================
# FLASK ESPECÍFICO
# ===================================

# Flask instance folder
instance/

# Flask session files
flask_session/

# Flask-SQLAlchemy database files
*.db
*.sqlite
*.sqlite3

# Flask uploads
uploads/
static/uploads/

# Flask logs
*.log
logs/
app.log
error.log
access.log

# Flask cache
.flask_cache/

# Flask-Migrate
migrations/versions/*.pyc

# ===================================
# BANCO DE DADOS
# ===================================

# SQLite databases
*.sqlite
*.sqlite3
*.db

# PostgreSQL dumps
*.sql
*.dump

# MySQL dumps
*.mysql

# Database backups
backup/
backups/
*.backup
*.bak

# ===================================
# ARQUIVOS DE CONFIGURAÇÃO SENSÍVEIS
# ===================================

# Arquivos de configuração com dados sensíveis
config.py
config_local.py
.env
.env.local
.env.production
.env.development

# Chaves e certificados
*.key
*.pem
*.crt
*.p12
*.pfx

# Arquivos de configuração do servidor
nginx.conf
apache.conf
uwsgi.ini
gunicorn.conf

# ===================================
# UPLOADS E ARQUIVOS DE USUÁRIO
# ===================================

# Uploads de usuários
uploads/
static/uploads/
media/
files/

# Anexos de dossiês
anexos/
documentos/
arquivos/

# Imagens temporárias
temp/
tmp/
temporary/

# ===================================
# IDEs E EDITORES
# ===================================

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
.idea/

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# SISTEMA OPERACIONAL
# ===================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# ESPECÍFICO DO PROJETO
# ===================================

# Scripts de teste temporários
test_*.py
teste_*.py
*_test.py
*_teste.py

# Arquivos de migração temporários
create_*.py
fix_*.py
verificar_*.py
add_*.py

# Backups de desenvolvimento
*.backup
*.old
*.orig
*.tmp

# Logs específicos do projeto
dossie.log
sistema.log
auditoria.log

# Arquivos de dados de teste
dados_teste/
test_data/

# ===================================
# DOCKER (caso use no futuro)
# ===================================

# Docker
Dockerfile.dev
docker-compose.override.yml
.dockerignore

# ===================================
# OUTROS
# ===================================

# Arquivos compactados
*.zip
*.tar.gz
*.rar
*.7z

# Arquivos temporários
*.tmp
*.temp
*.cache

# Arquivos de lock
*.lock

# Node.js (caso tenha frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Sass
.sass-cache/
*.css.map

# Arquivos de monitoramento
.monitor
.watchdog
