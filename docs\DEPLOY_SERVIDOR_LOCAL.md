# 🏠 DEPLOY NO SERVIDOR LOCAL - Sistema de Dossiê Escolar

## 📋 **INFORMAÇÕES DO SERVIDOR**

- **🌐 IP Local**: `**********`
- **🏢 Ambiente**: Servidor Local/Intranet
- **🐳 Stack**: Docker Swarm + Traefik + Portainer
- **🔧 Acesso**: Rede local

---

## 🚀 **PASSO 1: PREPARAÇÃO DO SERVIDOR**

### **1.1 Conectar no Servidor**
```bash
# Conectar via SSH
ssh usuario@**********

# Ou se for acesso direto no servidor
# Abrir terminal diretamente
```

### **1.2 Atualizar Sistema**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y

# Instalar utilitários
sudo apt install -y curl wget git htop nano ufw net-tools
```

### **1.3 Configurar Firewall (Opcional)**
```bash
# Configurar UFW para rede local
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Permitir SSH
sudo ufw allow ssh

# Permitir HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Permitir Portainer
sudo ufw allow 9000/tcp

# Permitir Traefik Dashboard
sudo ufw allow 8080/tcp

# Permitir aplicação direta
sudo ufw allow 5000/tcp

# Permitir acesso da rede local (ajustar conforme sua rede)
sudo ufw allow from ********/24

# Ativar firewall
sudo ufw --force enable
```

---

## 🐳 **PASSO 2: INSTALAR DOCKER**

### **2.1 Instalar Docker**
```bash
# Remover versões antigas
sudo apt remove -y docker docker-engine docker.io containerd runc

# Instalar dependências
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Adicionar chave GPG do Docker
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Adicionar repositório
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Instalar Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Adicionar usuário ao grupo docker
sudo usermod -aG docker $USER

# Iniciar e habilitar Docker
sudo systemctl start docker
sudo systemctl enable docker

# Verificar instalação
docker --version
docker compose version
```

### **2.2 Reiniciar Sessão**
```bash
# Logout e login novamente para aplicar permissões do grupo docker
exit
# Conectar novamente
ssh usuario@**********
```

### **2.3 Configurar Docker Swarm**
```bash
# Inicializar Swarm com IP do servidor
docker swarm init --advertise-addr **********

# Criar redes overlay
docker network create --driver overlay traefik-public
docker network create --driver overlay app-network

# Verificar
docker node ls
docker network ls
```

---

## 📁 **PASSO 3: ESTRUTURA DE ARQUIVOS**

### **3.1 Criar Diretórios**
```bash
# Criar estrutura principal
sudo mkdir -p /opt/dossie-app
sudo chown -R $USER:$USER /opt/dossie-app
cd /opt/dossie-app

# Criar subdiretórios
mkdir -p {traefik/data,portainer,postgres/data,app,scripts,backups}

# Configurar permissões do Traefik
touch traefik/data/acme.json
chmod 600 traefik/data/acme.json
```

### **3.2 Transferir Código da Aplicação**

**Opção A - Via Git:**
```bash
cd /opt/dossie-app/app
git clone https://github.com/SEU_USUARIO/dossie_novo.git .
```

**Opção B - Via SCP (do seu computador):**
```bash
# No seu computador local
scp -r /caminho/para/dossie_novo/* usuario@**********:/opt/dossie-app/app/
```

**Opção C - Via rsync (recomendado):**
```bash
# No seu computador local
rsync -avz --progress /caminho/para/dossie_novo/ usuario@**********:/opt/dossie-app/app/
```

---

## ⚙️ **PASSO 4: CONFIGURAÇÃO DOS SERVIÇOS**

### **4.1 Arquivo de Variáveis (.env)**
```bash
cd /opt/dossie-app
cat > .env << 'EOF'
# Servidor Local
DOMAIN=**********
USE_IP=true

# Email para certificados (não usado com IP)
ACME_EMAIL=<EMAIL>

# Banco de dados
POSTGRES_DB=dossie_escola
POSTGRES_USER=dossie
POSTGRES_PASSWORD=DossieLocal@2024!

# Flask
SECRET_KEY=sua-chave-secreta-super-segura-aqui
FLASK_ENV=production

# URLs
DATABASE_URL=***************************************************/dossie_escola

# Configurações de rede
NETWORK_SUBNET=********/24
SERVER_IP=**********
EOF
```

### **4.2 Gerar SECRET_KEY**
```bash
# Gerar chave secreta
python3 -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(32))"

# Editar .env e substituir a chave
nano .env
```

### **4.3 Docker Compose - Traefik**
```bash
cat > docker-compose.traefik.yml << 'EOF'
version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    command:
      - --api.dashboard=true
      - --api.insecure=true  # Para acesso via IP local
      - --providers.docker=true
      - --providers.docker.swarmmode=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --log.level=INFO
      - --accesslog=true
      - --global.sendanonymoususage=false
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/data:/data
    networks:
      - traefik-public
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
      labels:
        - traefik.enable=true
        - traefik.http.routers.traefik.rule=Host(`**********`)
        - traefik.http.routers.traefik.service=api@internal
        - traefik.http.services.traefik.loadbalancer.server.port=8080

networks:
  traefik-public:
    external: true
EOF
```

### **4.4 Docker Compose - Portainer**
```bash
cat > docker-compose.portainer.yml << 'EOF'
version: '3.8'

services:
  portainer:
    image: portainer/portainer-ce:latest
    command: -H unix:///var/run/docker.sock
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - traefik-public
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.http.routers.portainer.rule=Host(`**********`) && PathPrefix(`/portainer`)
        - traefik.http.routers.portainer.entrypoints=web
        - traefik.http.services.portainer.loadbalancer.server.port=9000
        - traefik.http.middlewares.portainer-stripprefix.stripprefix.prefixes=/portainer
        - traefik.http.routers.portainer.middlewares=portainer-stripprefix

volumes:
  portainer_data:

networks:
  traefik-public:
    external: true
EOF
```

### **4.5 Docker Compose - PostgreSQL**
```bash
cat > docker-compose.postgres.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: dossie_escola
      POSTGRES_USER: dossie
      POSTGRES_PASSWORD: DossieLocal@2024!
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - app-network
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dossie -d dossie_escola"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:

networks:
  app-network:
    external: true
EOF
```

### **4.6 Dockerfile da Aplicação**
```bash
cd /opt/dossie-app/app
cat > Dockerfile << 'EOF'
FROM python:3.11-slim

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Criar usuário não-root
RUN useradd --create-home --shell /bin/bash app

# Definir diretório de trabalho
WORKDIR /app

# Copiar requirements e instalar dependências Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn

# Copiar código da aplicação
COPY . .

# Criar diretórios necessários
RUN mkdir -p static/uploads logs
RUN chown -R app:app /app

# Mudar para usuário não-root
USER app

# Expor porta
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5000/ || exit 1

# Comando de inicialização
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "3", "--timeout", "120", "--access-logfile", "-", "--error-logfile", "-", "app:app"]
EOF
```

### **4.7 Docker Compose - Aplicação**
```bash
cd /opt/dossie-app
cat > docker-compose.app.yml << 'EOF'
version: '3.8'

services:
  dossie-app:
    build: ./app
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***************************************************/dossie_escola
      - SECRET_KEY=${SECRET_KEY}
      - SERVER_NAME=**********
    ports:
      - "5000:5000"  # Acesso direto
    volumes:
      - app_uploads:/app/static/uploads
      - app_logs:/app/logs
    networks:
      - traefik-public
      - app-network
    depends_on:
      - postgres
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.http.routers.dossie.rule=Host(`**********`)
        - traefik.http.routers.dossie.entrypoints=web
        - traefik.http.services.dossie.loadbalancer.server.port=5000
        - traefik.http.middlewares.dossie-headers.headers.customrequestheaders.X-Forwarded-Proto=http
        - traefik.http.routers.dossie.middlewares=dossie-headers
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  app_uploads:
  app_logs:

networks:
  traefik-public:
    external: true
  app-network:
    external: true
EOF
```

---

## 🚀 **PASSO 5: DEPLOY DOS SERVIÇOS**

### **5.1 Script de Deploy Automático**
```bash
cat > deploy-local.sh << 'EOF'
#!/bin/bash
# Script de Deploy para Servidor Local

set -e

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

log "🚀 Iniciando deploy no servidor local **********"

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    error "Docker não está rodando!"
fi

# Verificar se Swarm está ativo
if ! docker info | grep -q "Swarm: active"; then
    error "Docker Swarm não está ativo!"
fi

# Carregar variáveis de ambiente
if [ -f .env ]; then
    source .env
    log "Variáveis de ambiente carregadas"
else
    warn "Arquivo .env não encontrado"
fi

# Deploy dos serviços em ordem
log "🔀 Fazendo deploy do Traefik..."
docker stack deploy -c docker-compose.traefik.yml traefik

log "📊 Fazendo deploy do Portainer..."
docker stack deploy -c docker-compose.portainer.yml portainer

log "🐘 Fazendo deploy do PostgreSQL..."
docker stack deploy -c docker-compose.postgres.yml postgres

log "⏳ Aguardando PostgreSQL inicializar..."
sleep 30

log "🏗️ Fazendo build da aplicação..."
cd app && docker build -t dossie-app:latest . && cd ..

log "🌐 Fazendo deploy da aplicação..."
docker stack deploy -c docker-compose.app.yml dossie

log "⏳ Aguardando serviços inicializarem..."
sleep 60

# Verificar status
log "📊 Verificando status dos serviços..."
docker stack ls
docker service ls

log "✅ Deploy concluído!"
log ""
log "🌐 URLs de Acesso (Rede Local):"
log "   📱 Aplicação: http://**********"
log "   📱 Aplicação (direta): http://**********:5000"
log "   📊 Portainer: http://**********:9000"
log "   🔀 Traefik: http://**********:8080"
log ""
log "🎉 Sistema rodando no servidor local!"
EOF

chmod +x deploy-local.sh
```

### **5.2 Executar Deploy**
```bash
# Executar script de deploy
./deploy-local.sh
```

---

## 🔧 **PASSO 6: CONFIGURAÇÃO INICIAL**

### **6.1 Script de Configuração do Banco**
```bash
cat > setup-local.sh << 'EOF'
#!/bin/bash
# Configuração inicial do banco e usuário admin

set -e

GREEN='\033[0;32m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

log "🗄️ Configurando banco de dados..."

# Aguardar aplicação estar pronta
sleep 60

# Encontrar container da aplicação
APP_CONTAINER=$(docker ps -q -f name=dossie_dossie-app | head -1)

if [ -z "$APP_CONTAINER" ]; then
    echo "❌ Container da aplicação não encontrado!"
    exit 1
fi

log "📦 Container encontrado: $APP_CONTAINER"

# Executar migrações
log "🔄 Executando migrações..."
docker exec $APP_CONTAINER flask db upgrade || {
    log "⚠️ Tentando inicializar banco..."
    docker exec $APP_CONTAINER flask db init || true
    docker exec $APP_CONTAINER flask db migrate -m "Initial migration" || true
    docker exec $APP_CONTAINER flask db upgrade || true
}

# Criar usuário admin
log "👤 Criando usuário administrador..."
docker exec $APP_CONTAINER python3 << 'PYTHON_EOF'
from app import create_app
from models import db, Usuario, Perfil, Escola

try:
    app = create_app()
    with app.app_context():
        print("🔗 Conectado ao banco")
        
        # Criar escola padrão
        escola = Escola.query.first()
        if not escola:
            escola = Escola(
                nome='Escola Local',
                cnpj='00.000.000/0001-00',
                endereco='Servidor Local - **********',
                telefone='(11) 0000-0000',
                email='<EMAIL>',
                situacao='ativa'
            )
            db.session.add(escola)
            db.session.commit()
            print("🏫 Escola criada")
        
        # Criar perfil admin
        perfil = Perfil.query.filter_by(perfil='Administrador Geral').first()
        if not perfil:
            perfil = Perfil(
                perfil='Administrador Geral',
                nome='Administrador Geral'
            )
            db.session.add(perfil)
            db.session.commit()
            print("👑 Perfil admin criado")
        
        # Criar usuário admin
        admin = Usuario.query.filter_by(email='<EMAIL>').first()
        if not admin:
            admin = Usuario(
                nome='Administrador Local',
                email='<EMAIL>',
                escola_id=escola.id,
                perfil_id=perfil.id_perfil,
                situacao='ativo'
            )
            admin.set_password('Admin@Local123')
            db.session.add(admin)
            db.session.commit()
            print("✅ Usuário admin criado!")
            print("📧 Email: <EMAIL>")
            print("🔑 Senha: Admin@Local123")
        else:
            print("ℹ️ Usuário admin já existe")
            
except Exception as e:
    print(f"❌ Erro: {e}")
PYTHON_EOF

log "✅ Configuração concluída!"
log ""
log "🌐 Acesse: http://**********"
log "👤 Login: <EMAIL>"
log "🔑 Senha: Admin@Local123"
EOF

chmod +x setup-local.sh
```

### **6.2 Executar Configuração**
```bash
# Executar configuração inicial
./setup-local.sh
```

---

## 🌐 **PASSO 7: ACESSAR O SISTEMA**

### **7.1 URLs de Acesso**

**Na rede local (qualquer computador da rede):**
- **📱 Sistema Principal**: `http://**********`
- **📱 Acesso Direto**: `http://**********:5000`
- **📊 Portainer**: `http://**********:9000`
- **🔀 Traefik Dashboard**: `http://**********:8080`

### **7.2 Credenciais Padrão**
- **Email**: `<EMAIL>`
- **Senha**: `Admin@Local123`

### **7.3 Teste de Conectividade**
```bash
# Testar do próprio servidor
curl -I http://**********

# Testar de outro computador da rede
ping **********
curl -I http://**********
```

---

## 📊 **MONITORAMENTO E MANUTENÇÃO**

### **8.1 Comandos Úteis**
```bash
# Status dos serviços
docker stack ls
docker service ls

# Logs da aplicação
docker service logs dossie_dossie-app --tail 50

# Logs do PostgreSQL
docker service logs postgres_postgres --tail 20

# Recursos do sistema
docker stats

# Espaço em disco
df -h
```

### **8.2 Backup Automático**
```bash
cat > backup-local.sh << 'EOF'
#!/bin/bash
# Backup automático do sistema

BACKUP_DIR="/opt/dossie-app/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

echo "💾 Iniciando backup..."

# Backup do banco
echo "🐘 Backup PostgreSQL..."
docker exec $(docker ps -q -f name=postgres_postgres) pg_dump -U dossie dossie_escola > $BACKUP_DIR/db_$DATE.sql

# Backup dos uploads
echo "📁 Backup dos arquivos..."
docker run --rm -v dossie_app_uploads:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/uploads_$DATE.tar.gz -C /data .

# Backup das configurações
echo "⚙️ Backup das configurações..."
tar czf $BACKUP_DIR/config_$DATE.tar.gz *.yml .env

# Limpar backups antigos (manter 7 dias)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "✅ Backup concluído em $BACKUP_DIR"
EOF

chmod +x backup-local.sh

# Configurar cron para backup diário
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/dossie-app/backup-local.sh") | crontab -
```

---

## ✅ **CONCLUSÃO**

🎉 **Sistema implantado com sucesso no servidor local!**

### **✅ O que foi configurado:**
- ✅ Docker Swarm ativo
- ✅ Traefik como reverse proxy
- ✅ Portainer para gerenciamento
- ✅ PostgreSQL como banco de dados
- ✅ Aplicação Flask em alta disponibilidade
- ✅ Backup automático configurado
- ✅ Monitoramento implementado

### **🌐 Acesso na rede local:**
- **Qualquer computador** da rede pode acessar via `http://**********`
- **Interface administrativa** via Portainer
- **Logs centralizados** via Traefik

### **🔧 Próximos passos:**
1. **Testar** todas as funcionalidades
2. **Configurar** usuários adicionais
3. **Personalizar** conforme necessário
4. **Monitorar** performance e logs

**Sua aplicação está rodando em produção no servidor local! 🚀**
