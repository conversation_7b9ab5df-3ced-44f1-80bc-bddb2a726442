aiohappyeyeballs==2.6.1
aiohttp==3.12.2
aiosignal==1.3.2
alembic==1.16.1
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
attrs==25.3.0
blinker==1.9.0
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
contourpy==1.3.2
cycler==0.12.1
dataclasses-json==0.6.7
Deprecated==1.2.18
distro==1.9.0
Django==5.0.14
django-cors-headers==4.7.0
django-filter==25.1
django-widget-tweaks==1.5.0
djangorestframework==3.16.0
faiss-cpu==1.11.0
Flask==3.1.1
Flask-Limiter==3.12
Flask-Migrate==4.1.0
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.2
fonttools==4.58.1
frozenlist==1.6.0
greenlet==3.2.2
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
kiwisolver==1.4.8
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.62
langchain-openai==0.3.18
langchain-text-splitters==0.3.8
langchain-xai==0.2.4
langsmith==0.3.42
limits==5.2.0
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
mcp==1.9.3
mcp-server==0.1.4
mdurl==0.1.2
multidict==6.4.4
mypy_extensions==1.1.0
numpy==2.2.6
openai==1.82.0
ordered-set==4.1.0
orjson==3.10.18
packaging==24.2
pillow==11.2.1
propcache==0.3.1
psutil==7.0.0
psycopg2-binary==2.9.10
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
pyparsing==3.2.3
pypdf==5.5.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.1.0
python-multipart==0.0.20
PyYAML==6.0.2
redis==6.2.0
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
scikit-learn==1.6.1
scipy==1.15.3
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
sqlparse==0.5.3
sse-starlette==2.3.6
starlette==0.47.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tqdm==4.67.1
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.34.3
Werkzeug==3.1.3
wrapt==1.17.2
WTForms==3.2.1
yarl==1.20.0
zstandard==0.23.0
