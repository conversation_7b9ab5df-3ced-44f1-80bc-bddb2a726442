{% extends "base_completo.html" %}

{% block title %}Dashboard - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-1">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </h1>
                <p class="text-muted mb-0">
                    Bem-vindo, {{ usuario.nome }} 
                    {% if usuario.perfil_obj.nome != 'Administrador Geral' %}
                    - {{ usuario.escola.nome }}
                    {% endif %}
                </p>
            </div>
            <div class="text-end">
                <small class="text-muted">
                    Último acesso: 
                    {% if usuario.ultimo_acesso %}
                        {{ usuario.ultimo_acesso.strftime('%d/%m/%Y às %H:%M') }}
                    {% else %}
                        Primeiro acesso
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Cards de Estatísticas -->
<div class="row mb-4">
    {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_escolas }}</h4>
                        <p class="card-text">Escolas Cadastradas</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-school fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-primary border-primary">
                <a href="{{ url_for('listar_escolas') }}" class="text-white text-decoration-none">
                    Ver todas <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_usuarios }}</h4>
                        <p class="card-text">Usuários {% if usuario.perfil_obj.nome != 'Administrador Geral' %}da Escola{% endif %}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-success border-success">
                <a href="{{ url_for('listar_usuarios') }}" class="text-white text-decoration-none">
                    Ver todos <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_dossies }}</h4>
                        <p class="card-text">Dossiês Cadastrados</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-info border-info">
                <a href="#" class="text-white text-decoration-none">
                    Ver todos <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-dark dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_movimentacoes }}</h4>
                        <p class="card-text">Movimentações</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-warning border-warning">
                <a href="#" class="text-dark text-decoration-none">
                    Ver todas <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos e Estatísticas -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Movimentações dos Últimos 30 Dias
                </h5>
            </div>
            <div class="card-body">
                <canvas id="movimentacoesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Status dos Dossiês
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Seção de Atividades Recentes -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    Dossiês Recentes
                </h5>
                <a href="#" class="btn btn-sm btn-outline-primary">Ver todos</a>
            </div>
            <div class="card-body">
                {% if dossies_recentes %}
                    <div class="list-group list-group-flush">
                        {% for dossie in dossies_recentes %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ dossie.numero_dossie }}/{{ dossie.ano }}</h6>
                                <p class="mb-1">{{ dossie.nome }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ dossie.data_cadastro.strftime('%d/%m/%Y') }}
                                    {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
                                    | {{ dossie.escola.nome }}
                                    {% endif %}
                                </small>
                            </div>
                            <div>
                                <span class="badge bg-{{ 'success' if dossie.status == 'ativo' else 'secondary' }}">
                                    {{ dossie.status.title() }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhum dossiê cadastrado ainda.</p>
                        <a href="#" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Cadastrar Primeiro Dossiê
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Movimentações Recentes
                </h5>
                <a href="#" class="btn btn-sm btn-outline-warning">Ver todas</a>
            </div>
            <div class="card-body">
                {% if movimentacoes_recentes %}
                    <div class="list-group list-group-flush">
                        {% for movimentacao in movimentacoes_recentes %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ movimentacao.solicitante.nome }}</h6>
                                    <p class="mb-1">{{ movimentacao.descricao[:50] }}{% if movimentacao.descricao|length > 50 %}...{% endif %}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ movimentacao.data_solicitacao.strftime('%d/%m/%Y') }}
                                        {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
                                        | {{ movimentacao.escola.nome }}
                                        {% endif %}
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-{{ 'warning' if movimentacao.status == 'pendente' else 'success' if movimentacao.status == 'aprovada' else 'info' if movimentacao.status == 'devolvida' else 'secondary' }}">
                                        {{ movimentacao.status.title() }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhuma movimentação registrada ainda.</p>
                        <a href="#" class="btn btn-warning">
                            <i class="fas fa-plus me-2"></i>
                            Nova Movimentação
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
                    <div class="col-md-2 mb-2">
                        <a href="{{ url_for('nova_escola') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-school me-2"></i>
                            Nova Escola
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-2 mb-2">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            Novo Usuário
                        </a>
                    </div>
                    
                    <div class="col-md-2 mb-2">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="fas fa-folder-plus me-2"></i>
                            Novo Dossiê
                        </a>
                    </div>
                    
                    <div class="col-md-2 mb-2">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-exchange-alt me-2"></i>
                            Nova Movimentação
                        </a>
                    </div>
                    
                    <div class="col-md-2 mb-2">
                        <a href="#" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-user-friends me-2"></i>
                            Novo Solicitante
                        </a>
                    </div>
                    
                    <div class="col-md-2 mb-2">
                        <a href="#" class="btn btn-outline-dark w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            Relatórios
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Gráfico de Movimentações
const ctxMovimentacoes = document.getElementById('movimentacoesChart').getContext('2d');
const movimentacoesChart = new Chart(ctxMovimentacoes, {
    type: 'line',
    data: {
        labels: ['1', '5', '10', '15', '20', '25', '30'],
        datasets: [{
            label: 'Movimentações',
            data: [12, 19, 3, 5, 2, 3, 10],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Gráfico de Status
const ctxStatus = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(ctxStatus, {
    type: 'doughnut',
    data: {
        labels: ['Ativos', 'Arquivados', 'Emprestados'],
        datasets: [{
            data: [{{ total_dossies * 0.7 | int }}, {{ total_dossies * 0.2 | int }}, {{ total_dossies * 0.1 | int }}],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(108, 117, 125, 0.8)',
                'rgba(255, 193, 7, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
{% endblock %}
