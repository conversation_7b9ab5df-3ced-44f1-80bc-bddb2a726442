/* Sistema de Controle de Dossiê Escolar - Estilos Customizados */

/* Variáveis CSS */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Estilos gerais */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navbar customizada */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* Cards customizados */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Botões customizados */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Tabelas customizadas */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badges customizados */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: var(--border-radius);
}

/* Formulários customizados */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Alertas customizados */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* Paginação customizada */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Dashboard específico */
.dashboard-card {
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: scale(1.02);
}

.dashboard-stat {
    font-size: 2rem;
    font-weight: bold;
}

/* Lista de itens customizada */
.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: var(--light-color);
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Jumbotron customizado */
.jumbotron {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: var(--border-radius);
}

/* Ícones */
.fa, .fas, .far {
    margin-right: 0.25rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Tooltip customizado */
.tooltip {
    font-size: 0.875rem;
}

/* Breadcrumb customizado */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--secondary-color);
}

/* Status indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background-color: var(--success-color);
}

.status-indicator.inactive {
    background-color: var(--danger-color);
}

.status-indicator.pending {
    background-color: var(--warning-color);
}

/* Sidebar (para futuras implementações) */
.sidebar {
    background-color: #fff;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

/* Footer customizado */
footer {
    margin-top: auto;
    background-color: var(--light-color) !important;
    border-top: 1px solid #dee2e6;
}

/* Utilitários */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}
