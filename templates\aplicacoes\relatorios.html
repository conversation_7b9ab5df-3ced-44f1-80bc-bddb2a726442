<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Sistema Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-graduation-cap me-2"></i>Sistema Modular
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>
                    <i class="fas fa-chart-bar me-2"></i>
                    Aplicação RELATÓRIOS
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Relatórios</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Central de Relatórios</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-friends me-2"></i>
                                            Movimentações por Solicitante
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Relatório detalhado de todas as movimentações agrupadas por solicitante.</p>
                                        <button class="btn btn-primary">
                                            <i class="fas fa-file-pdf me-2"></i>Gerar PDF
                                        </button>
                                        <button class="btn btn-outline-primary">
                                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Documentos não Devolvidos
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Lista de documentos emprestados que não foram devolvidos no prazo.</p>
                                        <button class="btn btn-warning">
                                            <i class="fas fa-file-pdf me-2"></i>Gerar PDF
                                        </button>
                                        <button class="btn btn-outline-warning">
                                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-history me-2"></i>
                                            Histórico de Acessos
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Relatório de auditoria com histórico de acessos e ações dos usuários.</p>
                                        <button class="btn btn-info">
                                            <i class="fas fa-file-pdf me-2"></i>Gerar PDF
                                        </button>
                                        <button class="btn btn-outline-info">
                                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-school me-2"></i>
                                            Dossiês por Escola/Ano
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Estatísticas de dossiês organizadas por escola e ano letivo.</p>
                                        <button class="btn btn-success">
                                            <i class="fas fa-file-pdf me-2"></i>Gerar PDF
                                        </button>
                                        <button class="btn btn-outline-success">
                                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-dark">
                    <h6><i class="fas fa-info-circle me-2"></i>Aplicação RELATÓRIOS</h6>
                    <p class="mb-0">
                        Aplicação modular para geração de relatórios conforme CLAUDE.md.
                        Gera relatórios em PDF e Excel com dados de movimentações, acessos e estatísticas.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
