{% extends "admin/base.html" %}

{% block title %}Logs do Sistema{% endblock %}
{% block header %}Logs do Sistema{% endblock %}

{% block content %}
<!-- Estatísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-clipboard-list fa-2x mb-2 text-primary"></i>
                <h3>{{ stats.total_auditoria }}</h3>
                <p class="mb-0">Logs de Auditoria</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2 text-warning"></i>
                <h3>{{ stats.total_sistema }}</h3>
                <p class="mb-0">Logs do Sistema</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-2 text-success"></i>
                <h3>{{ stats.logs_hoje_auditoria }}</h3>
                <p class="mb-0">Auditoria Hoje</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-2 text-info"></i>
                <h3>{{ stats.logs_hoje_sistema }}</h3>
                <p class="mb-0">Sistema Hoje</p>
            </div>
        </div>
    </div>
</div>

<!-- Logs de Auditoria -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-list me-2"></i>Logs de Auditoria (Últimos 50)</h5>
            </div>
            <div class="card-body">
                {% if logs_auditoria %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Data/Hora</th>
                                    <th>Usuário</th>
                                    <th>Ação</th>
                                    <th>Item</th>
                                    <th>IP</th>
                                    <th>Detalhes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs_auditoria %}
                                <tr>
                                    <td>
                                        <small>{{ log.data_hora.strftime('%d/%m/%Y') }}</small><br>
                                        <small class="text-muted">{{ log.data_hora.strftime('%H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ log.usuario.nome if log.usuario else 'Sistema' }}</strong><br>
                                        <small class="text-muted">{{ log.usuario.email if log.usuario else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ log.acao }}</span>
                                    </td>
                                    <td>{{ log.item_alterado or '-' }}</td>
                                    <td>
                                        <small>{{ log.ip_address or '-' }}</small>
                                    </td>
                                    <td>
                                        {% if log.detalhes %}
                                            <small>{{ log.detalhes[:50] }}{% if log.detalhes|length > 50 %}...{% endif %}</small>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhum log de auditoria encontrado</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Logs do Sistema -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Logs do Sistema (Últimos 50)</h5>
            </div>
            <div class="card-body">
                {% if logs_sistema %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Data/Hora</th>
                                    <th>Nível</th>
                                    <th>Usuário</th>
                                    <th>Módulo</th>
                                    <th>Função</th>
                                    <th>Mensagem</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs_sistema %}
                                <tr>
                                    <td>
                                        <small>{{ log.data_hora.strftime('%d/%m/%Y') }}</small><br>
                                        <small class="text-muted">{{ log.data_hora.strftime('%H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        {% if log.nivel_erro == 'ERROR' %}
                                            <span class="badge bg-danger">{{ log.nivel_erro }}</span>
                                        {% elif log.nivel_erro == 'WARNING' %}
                                            <span class="badge bg-warning">{{ log.nivel_erro }}</span>
                                        {% elif log.nivel_erro == 'INFO' %}
                                            <span class="badge bg-info">{{ log.nivel_erro }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ log.nivel_erro }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.usuario %}
                                            <strong>{{ log.usuario.nome }}</strong><br>
                                            <small class="text-muted">{{ log.usuario.email }}</small>
                                        {% else %}
                                            <span class="text-muted">Sistema</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ log.modulo or '-' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ log.funcao or '-' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ log.mensagem_erro[:100] }}{% if log.mensagem_erro|length > 100 %}...{% endif %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhum log do sistema encontrado</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
