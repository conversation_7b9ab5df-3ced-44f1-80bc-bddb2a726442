# 📋 PASSO A PASSO - Deploy Servidor Local *********5

## 🎯 **OBJETIVO**
Colocar o Sistema de Dossiê Escolar rodando no servidor local `*********5` usando Docker Swarm, Traefik e Portainer.

---

## 📦 **ARQUIVOS INCLUÍDOS**

✅ `env-servidor-local` - Configurações do ambiente
✅ `docker-compose.traefik.yml` - Configuração do Traefik
✅ `docker-compose.portainer.yml` - Configuração do Portainer  
✅ `docker-compose.postgres.yml` - Configuração do PostgreSQL
✅ `docker-compose.app.yml` - Configuração da aplicação
✅ `Dockerfile` - Imagem da aplicação
✅ `deploy.sh` - Script de deploy automático
✅ `PASSO_A_PASSO.md` - Este guia

---

## 🚀 **PASSO 1: PREPARAR O SERVIDOR**

### **1.1 Conectar no Servidor**
```bash
ssh usuario@*********5
```

### **1.2 Instalar Docker (se não estiver instalado)**
```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Adicionar usuário ao grupo docker
sudo usermod -aG docker $USER

# Reiniciar sessão
exit
ssh usuario@*********5
```

### **1.3 Inicializar Docker Swarm**
```bash
docker swarm init --advertise-addr *********5
```

---

## 📁 **PASSO 2: TRANSFERIR ARQUIVOS**

### **2.1 Criar Diretório no Servidor**
```bash
sudo mkdir -p /var/www/dossie_escolar
sudo chown -R $USER:$USER /var/www/dossie_escolar
cd /var/www/dossie_escolar
```

### **2.2 Transferir Todos os Arquivos**

**Do seu computador local:**
```bash
# Transferir todos os arquivos do projeto
scp -r /caminho/para/dossie_novo/* usuario@*********5:/var/www/dossie_escolar/

# Ou usar rsync (recomendado)
rsync -avz --progress /caminho/para/dossie_novo/ usuario@*********5:/var/www/dossie_escolar/
```

**Ou copiar arquivo por arquivo:**
```bash
scp env-servidor-local usuario@*********5:/var/www/dossie_escolar/
scp docker-compose.*.yml usuario@*********5:/var/www/dossie_escolar/
scp Dockerfile usuario@*********5:/var/www/dossie_escolar/
scp deploy.sh usuario@*********5:/var/www/dossie_escolar/
# ... copiar todos os arquivos da aplicação
```

---

## ⚙️ **PASSO 3: CONFIGURAR AMBIENTE**

### **3.1 No Servidor, Preparar Configuração**
```bash
cd /var/www/dossie_escolar

# Copiar arquivo de configuração
cp env-servidor-local .env

# Dar permissão aos scripts
chmod +x *.sh
```

### **3.2 Gerar SECRET_KEY (Opcional)**
```bash
# O script fará isso automaticamente, mas você pode fazer manualmente:
python3 -c "import secrets; print(secrets.token_urlsafe(32))"

# Editar .env e substituir a SECRET_KEY
nano .env
```

---

## 🚀 **PASSO 4: EXECUTAR DEPLOY**

### **4.1 Executar Script de Deploy**
```bash
cd /var/www/dossie_escolar
./deploy.sh
```

### **4.2 Aguardar Conclusão**
O script irá:
- ✅ Verificar Docker e Swarm
- ✅ Criar redes necessárias
- ✅ Fazer deploy do Traefik
- ✅ Fazer deploy do Portainer
- ✅ Fazer deploy do PostgreSQL
- ✅ Fazer build da aplicação
- ✅ Fazer deploy da aplicação
- ✅ Executar migrações do banco
- ✅ Criar usuário administrador

---

## 🌐 **PASSO 5: ACESSAR O SISTEMA**

### **5.1 URLs de Acesso**
- **📱 Sistema Principal**: `http://*********5`
- **📱 Acesso Direto**: `http://*********5:5000`
- **📊 Portainer**: `http://*********5:9000`
- **🔀 Traefik Dashboard**: `http://*********5:8080`

### **5.2 Credenciais de Login**
- **Email**: `<EMAIL>`
- **Senha**: `Admin@Local123`

### **5.3 Testar Acesso**
```bash
# Testar do próprio servidor
curl -I http://*********5

# De outro computador da rede
ping *********5
curl -I http://*********5
```

---

## 🔧 **COMANDOS ÚTEIS**

### **Verificar Status**
```bash
# Status das stacks
docker stack ls

# Status dos serviços
docker service ls

# Detalhes de um serviço
docker service ps dossie_dossie-app
```

### **Ver Logs**
```bash
# Logs da aplicação
docker service logs dossie_dossie-app --tail 50

# Logs do PostgreSQL
docker service logs postgres_postgres --tail 20

# Logs do Traefik
docker service logs traefik_traefik --tail 20
```

### **Restart Serviços**
```bash
# Restart da aplicação
docker service update --force dossie_dossie-app

# Restart do banco
docker service update --force postgres_postgres

# Restart do Traefik
docker service update --force traefik_traefik
```

### **Escalar Aplicação**
```bash
# Aumentar réplicas
docker service scale dossie_dossie-app=3

# Diminuir réplicas
docker service scale dossie_dossie-app=1
```

---

## 🛠️ **RESOLUÇÃO DE PROBLEMAS**

### **Problema: Script falha**
```bash
# Verificar logs do script
./deploy.sh 2>&1 | tee deploy.log

# Verificar se Docker está rodando
sudo systemctl status docker

# Verificar se Swarm está ativo
docker info | grep Swarm
```

### **Problema: Aplicação não responde**
```bash
# Verificar se container está rodando
docker ps | grep dossie

# Verificar logs
docker service logs dossie_dossie-app --tail 100

# Restart forçado
docker service update --force dossie_dossie-app
```

### **Problema: Banco não conecta**
```bash
# Verificar PostgreSQL
docker service ps postgres_postgres

# Testar conexão
docker exec -it $(docker ps -q -f name=postgres_postgres) psql -U dossie -d dossie_escola -c "SELECT 1;"
```

### **Problema: Portainer não abre**
```bash
# Verificar se está rodando
docker service ps portainer_portainer

# Acesso direto
curl -I http://*********5:9000
```

---

## 📊 **BACKUP E MANUTENÇÃO**

### **Backup do Banco**
```bash
# Backup manual
docker exec $(docker ps -q -f name=postgres_postgres) pg_dump -U dossie dossie_escola > backup_$(date +%Y%m%d).sql
```

### **Backup dos Uploads**
```bash
# Backup dos arquivos
docker run --rm -v dossie_app_uploads:/data -v /opt/dossie-app/backups:/backup alpine tar czf /backup/uploads_$(date +%Y%m%d).tar.gz -C /data .
```

### **Atualizar Aplicação**
```bash
# Após alterar código
cd /var/www/dossie_escolar
docker build -t dossie-app:latest .
docker service update --image dossie-app:latest dossie_dossie-app
```

---

## ✅ **VERIFICAÇÃO FINAL**

### **Checklist de Funcionamento:**
- [ ] Traefik Dashboard acessível em `:8080`
- [ ] Portainer acessível em `:9000`
- [ ] Aplicação acessível em `:80` e `:5000`
- [ ] Login funcionando com credenciais padrão
- [ ] Criação de dossiê funcionando
- [ ] Upload de arquivos funcionando

### **Checklist de Rede:**
- [ ] Servidor acessível de outros computadores da rede
- [ ] Firewall configurado (se necessário)
- [ ] DNS local configurado (se necessário)

---

## 🎉 **CONCLUSÃO**

Após seguir todos os passos:

✅ **Sistema rodando** em `http://*********5`
✅ **Alta disponibilidade** com Docker Swarm
✅ **Gerenciamento visual** com Portainer
✅ **Proxy reverso** com Traefik
✅ **Banco PostgreSQL** persistente
✅ **Backup** configurado
✅ **Logs** centralizados

**Sua aplicação está pronta para uso na rede local! 🚀**

---

## 📞 **SUPORTE**

### **Em caso de problemas:**
1. Verificar logs: `docker service logs dossie_dossie-app`
2. Verificar status: `docker service ls`
3. Restart: `docker service update --force dossie_dossie-app`
4. Backup: Sempre fazer backup antes de mudanças

### **Comandos de emergência:**
```bash
# Parar tudo
docker stack rm dossie traefik portainer postgres

# Limpar sistema
docker system prune -f

# Reiniciar Docker
sudo systemctl restart docker

# Executar deploy novamente
./deploy.sh
```

**Sistema robusto e pronto para produção! 🎯**
