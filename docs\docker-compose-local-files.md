# 📁 ARQUIVOS DOCKER-COMPOSE PARA SERVIDOR LOCAL

## 🌐 **Configuração para IP: ************

### **📄 .env - Variáveis de Ambiente**
```bash
# Servidor Local
DOMAIN=**********
USE_IP=true
SERVER_IP=**********

# Email para certificados (não usado com IP)
ACME_EMAIL=<EMAIL>

# Banco de dados
POSTGRES_DB=dossie_escola
POSTGRES_USER=dossie
POSTGRES_PASSWORD=DossieLocal@2024!Seguro

# Flask
SECRET_KEY=gere-uma-chave-secreta-de-32-caracteres-aqui
FLASK_ENV=production

# URLs
DATABASE_URL=*********************************************************/dossie_escola

# Configurações de rede
NETWORK_SUBNET=********/24
```

---

## 🔀 **docker-compose.traefik.yml**

```yaml
version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    command:
      - --api.dashboard=true
      - --api.insecure=true  # Para acesso via IP local
      - --providers.docker=true
      - --providers.docker.swarmmode=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --log.level=INFO
      - --accesslog=true
      - --global.sendanonymoususage=false
      - --ping=true
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/data:/data
    networks:
      - traefik-public
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      labels:
        - traefik.enable=true
        - traefik.http.routers.traefik-dashboard.rule=Host(`**********`) && (PathPrefix(`/api`) || PathPrefix(`/dashboard`))
        - traefik.http.routers.traefik-dashboard.service=api@internal
        - traefik.http.routers.traefik-dashboard.entrypoints=web
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  traefik-public:
    external: true
```

---

## 📊 **docker-compose.portainer.yml**

```yaml
version: '3.8'

services:
  portainer:
    image: portainer/portainer-ce:latest
    command: -H unix:///var/run/docker.sock
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    networks:
      - traefik-public
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      labels:
        - traefik.enable=true
        - traefik.http.routers.portainer.rule=Host(`**********`) && PathPrefix(`/portainer`)
        - traefik.http.routers.portainer.entrypoints=web
        - traefik.http.services.portainer.loadbalancer.server.port=9000
        - traefik.http.middlewares.portainer-stripprefix.stripprefix.prefixes=/portainer
        - traefik.http.routers.portainer.middlewares=portainer-stripprefix

volumes:
  portainer_data:

networks:
  traefik-public:
    external: true
```

---

## 🐘 **docker-compose.postgres.yml**

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
      - ./postgres/init:/docker-entrypoint-initdb.d
    networks:
      - app-network
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres-backup:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    networks:
      - app-network
    deploy:
      replicas: 0  # Executar manualmente
      restart_policy:
        condition: none
    command: >
      sh -c "
        echo 'Backup service ready. Execute manually with:'
        echo 'docker service update --replicas 1 postgres_postgres-backup'
        sleep infinity
      "

volumes:
  postgres_data:

networks:
  app-network:
    external: true
```

---

## 🌐 **docker-compose.app.yml**

```yaml
version: '3.8'

services:
  dossie-app:
    image: dossie-app:latest
    build:
      context: ./app
      dockerfile: Dockerfile
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SECRET_KEY=${SECRET_KEY}
      - SERVER_NAME=**********
      - UPLOAD_FOLDER=/app/static/uploads
      - MAX_CONTENT_LENGTH=16777216  # 16MB
    ports:
      - "5000:5000"  # Acesso direto
    volumes:
      - app_uploads:/app/static/uploads
      - app_logs:/app/logs
    networks:
      - traefik-public
      - app-network
    depends_on:
      - postgres
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.http.routers.dossie.rule=Host(`**********`)
        - traefik.http.routers.dossie.entrypoints=web
        - traefik.http.services.dossie.loadbalancer.server.port=5000
        - traefik.http.middlewares.dossie-headers.headers.customrequestheaders.X-Forwarded-Proto=http
        - traefik.http.middlewares.dossie-headers.headers.customrequestheaders.X-Forwarded-For=$$remote_addr
        - traefik.http.routers.dossie.middlewares=dossie-headers
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    networks:
      - app-network
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
      restart_policy:
        condition: on-failure
    command: redis-server --appendonly yes --maxmemory 100mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  app_uploads:
  app_logs:

networks:
  traefik-public:
    external: true
  app-network:
    external: true
```

---

## 📊 **docker-compose.monitoring.yml** (Opcional)

```yaml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - traefik-public
      - app-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.http.routers.prometheus.rule=Host(`**********`) && PathPrefix(`/prometheus`)
        - traefik.http.routers.prometheus.entrypoints=web
        - traefik.http.services.prometheus.loadbalancer.server.port=9090
        - traefik.http.middlewares.prometheus-stripprefix.stripprefix.prefixes=/prometheus
        - traefik.http.routers.prometheus.middlewares=prometheus-stripprefix

  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://**********/grafana
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - traefik-public
      - app-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
      labels:
        - traefik.enable=true
        - traefik.http.routers.grafana.rule=Host(`**********`) && PathPrefix(`/grafana`)
        - traefik.http.routers.grafana.entrypoints=web
        - traefik.http.services.grafana.loadbalancer.server.port=3000

volumes:
  prometheus_data:
  grafana_data:

networks:
  traefik-public:
    external: true
  app-network:
    external: true
```

---

## 🐳 **Dockerfile**

```dockerfile
FROM python:3.11-slim

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Criar usuário não-root
RUN useradd --create-home --shell /bin/bash app

# Definir diretório de trabalho
WORKDIR /app

# Copiar requirements e instalar dependências Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn

# Copiar código da aplicação
COPY . .

# Criar diretórios necessários
RUN mkdir -p static/uploads logs
RUN chown -R app:app /app

# Mudar para usuário não-root
USER app

# Expor porta
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5000/ || exit 1

# Comando de inicialização
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "3", "--timeout", "120", "--access-logfile", "-", "--error-logfile", "-", "app:app"]
```

---

## 🚀 **Script de Deploy Completo**

```bash
#!/bin/bash
# deploy-local-complete.sh

set -e

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[WARNING] $1${NC}"; }
error() { echo -e "${RED}[ERROR] $1${NC}"; exit 1; }

log "🚀 Deploy Completo - Servidor Local **********"

# Verificações
docker info > /dev/null 2>&1 || error "Docker não está rodando!"
docker info | grep -q "Swarm: active" || error "Docker Swarm não está ativo!"

# Carregar variáveis
[ -f .env ] && source .env || warn "Arquivo .env não encontrado"

# Criar redes se não existirem
docker network create --driver overlay traefik-public 2>/dev/null || true
docker network create --driver overlay app-network 2>/dev/null || true

# Deploy em ordem
log "🔀 Deploy Traefik..."
docker stack deploy -c docker-compose.traefik.yml traefik

log "📊 Deploy Portainer..."
docker stack deploy -c docker-compose.portainer.yml portainer

log "🐘 Deploy PostgreSQL..."
docker stack deploy -c docker-compose.postgres.yml postgres

log "⏳ Aguardando PostgreSQL..."
sleep 30

log "🏗️ Build da aplicação..."
cd app && docker build -t dossie-app:latest . && cd ..

log "🌐 Deploy da aplicação..."
docker stack deploy -c docker-compose.app.yml dossie

log "⏳ Aguardando inicialização..."
sleep 60

# Status
log "📊 Status dos serviços:"
docker stack ls
docker service ls

log "✅ Deploy concluído!"
log ""
log "🌐 URLs de Acesso:"
log "   📱 Aplicação: http://**********"
log "   📱 Direto: http://**********:5000"
log "   📊 Portainer: http://**********:9000"
log "   🔀 Traefik: http://**********:8080"
log ""
log "🎉 Sistema rodando no servidor local!"
```

**Todos os arquivos estão prontos para deploy no servidor local **********! 🎯**
