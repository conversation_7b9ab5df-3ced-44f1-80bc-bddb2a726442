<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Permissões - Sistema de Dossiê</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .permission-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .permission-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
        }
        .permission-body {
            padding: 20px;
        }
        .module-section {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .permission-checkbox {
            margin: 5px 0;
        }
        .profile-tab {
            cursor: pointer;
            transition: all 0.3s;
        }
        .profile-tab:hover {
            background-color: #f8f9fa;
        }
        .profile-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-shield-alt me-2"></i>Gerenciar Permissões por Perfil</h2>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </a>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Lista de Perfis -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users me-2"></i>Perfis</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            {% for perfil in perfis %}
                            <div class="list-group-item profile-tab" 
                                 data-perfil-id="{{ perfil.id_perfil }}"
                                 onclick="showProfile({{ perfil.id_perfil }})">
                                <i class="fas fa-user-tag me-2"></i>
                                <strong>{{ perfil.perfil }}</strong>
                                <br>
                                <small class="text-muted">{{ perfil.descricao or 'Sem descrição' }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Permissões do Perfil -->
            <div class="col-md-9">
                {% for perfil in perfis %}
                <div class="permission-card" id="perfil-{{ perfil.id_perfil }}" style="display: none;">
                    <div class="permission-header">
                        <h4><i class="fas fa-user-shield me-2"></i>{{ perfil.perfil }}</h4>
                        <p class="mb-0">{{ perfil.descricao or 'Configure as permissões deste perfil' }}</p>
                    </div>
                    
                    <div class="permission-body">
                        <form method="POST" action="{{ url_for('permissao.atualizar_perfil', perfil_id=perfil.id_perfil) }}">
                            
                            {% if perfil.perfil == 'Administrador Geral' %}
                            <div class="alert alert-info">
                                <i class="fas fa-crown me-2"></i>
                                <strong>Administrador Geral</strong> tem acesso total ao sistema automaticamente.
                            </div>
                            {% endif %}
                            
                            <!-- Permissões por Módulo -->
                            {% for modulo, permissoes in modulos.items() %}
                            <div class="module-section">
                                <h6 class="text-uppercase fw-bold mb-3">
                                    <i class="fas fa-folder me-2"></i>{{ modulo.title() }}
                                </h6>
                                
                                <div class="row">
                                    {% for permissao in permissoes %}
                                    <div class="col-md-6 col-lg-4">
                                        <div class="form-check permission-checkbox">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   name="permissoes" 
                                                   value="{{ permissao.id }}"
                                                   id="perm_{{ perfil.id_perfil }}_{{ permissao.id }}"
                                                   {% if permissao.id in perfil_permissoes.get(perfil.id_perfil, []) %}checked{% endif %}
                                                   {% if perfil.perfil == 'Administrador Geral' %}disabled{% endif %}>
                                            <label class="form-check-label" 
                                                   for="perm_{{ perfil.id_perfil }}_{{ permissao.id }}">
                                                <strong>{{ permissao.acao.title() }}</strong>
                                                <br>
                                                <small class="text-muted">{{ permissao.descricao }}</small>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                            
                            <!-- Botões de Ação -->
                            <div class="d-flex justify-content-between mt-4">
                                <div>
                                    <button type="button" class="btn btn-outline-primary" onclick="selectAll({{ perfil.id_perfil }})">
                                        <i class="fas fa-check-double me-2"></i>Selecionar Todas
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="selectNone({{ perfil.id_perfil }})">
                                        <i class="fas fa-times me-2"></i>Desmarcar Todas
                                    </button>
                                </div>
                                
                                {% if perfil.perfil != 'Administrador Geral' %}
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Salvar Permissões
                                </button>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
                {% endfor %}
                
                <!-- Mensagem inicial -->
                <div class="text-center py-5" id="select-profile">
                    <i class="fas fa-hand-pointer fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Selecione um perfil para configurar as permissões</h4>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showProfile(perfilId) {
            // Esconder todos os perfis
            document.querySelectorAll('.permission-card').forEach(card => {
                card.style.display = 'none';
            });
            
            // Esconder mensagem inicial
            document.getElementById('select-profile').style.display = 'none';
            
            // Mostrar perfil selecionado
            document.getElementById('perfil-' + perfilId).style.display = 'block';
            
            // Atualizar tabs
            document.querySelectorAll('.profile-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector('[data-perfil-id="' + perfilId + '"]').classList.add('active');
        }
        
        function selectAll(perfilId) {
            const container = document.getElementById('perfil-' + perfilId);
            const checkboxes = container.querySelectorAll('input[type="checkbox"]:not([disabled])');
            checkboxes.forEach(cb => cb.checked = true);
        }
        
        function selectNone(perfilId) {
            const container = document.getElementById('perfil-' + perfilId);
            const checkboxes = container.querySelectorAll('input[type="checkbox"]:not([disabled])');
            checkboxes.forEach(cb => cb.checked = false);
        }
        
        // Mostrar primeiro perfil por padrão
        {% if perfis %}
        showProfile({{ perfis[0].id_perfil }});
        {% endif %}
    </script>
</body>
</html>
