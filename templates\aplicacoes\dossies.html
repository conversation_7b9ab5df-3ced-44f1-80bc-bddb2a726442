<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dossiês - Sistema Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-info">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-graduation-cap me-2"></i>Sistema Modular
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>
                    <i class="fas fa-folder me-2"></i>
                    Aplicação DOSSIÊS
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Dossiês</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Gestão de Dossiês</h5>
                    </div>
                    <div class="card-body text-center py-5">
                        <i class="fas fa-folder-open fa-4x text-info mb-4"></i>
                        <h4>Aplicação DOSSIÊS</h4>
                        <p class="lead">Gestão completa de dossiês escolares</p>
                        <p>Local, pasta, número do dossiê, ano, nome, CPF, nome do pai, nome da mãe, status, tipo de documento, data de cadastro, data de arquivamento, escola, observações, foto</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-plus fa-2x text-info mb-2"></i>
                                        <h6>Novo Dossiê</h6>
                                        <small>Cadastrar novo dossiê</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-search fa-2x text-info mb-2"></i>
                                        <h6>Buscar</h6>
                                        <small>Busca avançada</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-archive fa-2x text-info mb-2"></i>
                                        <h6>Arquivar</h6>
                                        <small>Arquivamento</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-upload fa-2x text-info mb-2"></i>
                                        <h6>Documentos</h6>
                                        <small>Upload de arquivos</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Aplicação DOSSIÊS</h6>
                    <p class="mb-0">
                        Aplicação modular para gestão de dossiês conforme especificação CLAUDE.md.
                        Controla local, pasta, número, ano, nome, CPF, pais, status, documentos e observações.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
