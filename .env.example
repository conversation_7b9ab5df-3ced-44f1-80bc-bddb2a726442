# Configurações do Sistema de Controle de Dossiê Escolar
# Copie este arquivo para .env e ajuste as configurações conforme necessário

# Chave secreta para sessões (gere uma nova para produção)
SECRET_KEY=sua_chave_secreta_aqui

# Configuração do banco de dados
# Para SQLite (padrão):
DATABASE_URL=sqlite:///dossie_escolar.db

# Para PostgreSQL:
# DATABASE_URL=postgresql://usuario:senha@localhost/dossie_escolar

# Para MySQL:
# DATABASE_URL=mysql://usuario:senha@localhost/dossie_escolar

# Ambiente de execução
FLASK_ENV=development

# Configurações de upload
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# Configurações de email (para futuras implementações)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=sua_senha_de_app
