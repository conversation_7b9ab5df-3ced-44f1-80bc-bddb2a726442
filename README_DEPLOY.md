# 🚀 DEPLOY SERVIDOR LOCAL - Sistema de Dossiê Escolar

## 📦 **ARQUIVOS CRIADOS**

✅ **Configuração:**
- `env-servidor-local` - Variáveis de ambiente
- `PASSO_A_PASSO.md` - <PERSON><PERSON><PERSON> completo

✅ **Docker Compose:**
- `docker-compose.traefik.yml` - <PERSON><PERSON><PERSON><PERSON> (proxy reverso)
- `docker-compose.portainer.yml` - <PERSON><PERSON><PERSON> (gerenciamento)
- `docker-compose.postgres.yml` - PostgreSQL (banco de dados)
- `docker-compose.app.yml` - Aplicação Flask

✅ **Scripts:**
- `Dockerfile` - Imagem da aplicação
- `deploy.sh` - Deploy automático
- `backup.sh` - Backup automático
- `monitor.sh` - Monitoramento

---

## 🎯 **RESUMO RÁPIDO**

### **1. Preparar Servidor:**
```bash
# Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Inicializar Swarm
docker swarm init --advertise-addr **********
```

### **2. Transferir Arquivos:**
```bash
# Do seu computador para o servidor
rsync -avz /caminho/para/dossie_novo/ usuario@**********:/opt/dossie-app/
```

### **3. Executar Deploy:**
```bash
cd /opt/dossie-app
cp env-servidor-local .env
chmod +x *.sh
./deploy.sh
```

### **4. Acessar Sistema:**
- **Sistema**: `http://**********`
- **Login**: `<EMAIL>` / `Admin@Local123`

---

## 📋 **COMANDOS ÚTEIS**

### **Status:**
```bash
./monitor.sh              # Monitoramento completo
docker service ls         # Lista serviços
docker stack ls           # Lista stacks
```

### **Logs:**
```bash
docker service logs dossie_dossie-app --tail 50
docker service logs postgres_postgres --tail 20
```

### **Backup:**
```bash
./backup.sh               # Backup manual
```

### **Restart:**
```bash
docker service update --force dossie_dossie-app
```

---

## 🌐 **URLs DE ACESSO**

- **📱 Sistema Principal**: `http://**********`
- **📱 Acesso Direto**: `http://**********:5000`
- **📊 Portainer**: `http://**********:9000`
- **🔀 Traefik**: `http://**********:8080`

---

## 🔧 **ESTRUTURA FINAL**

```
/opt/dossie-app/
├── .env                           # Configurações
├── docker-compose.traefik.yml     # Traefik
├── docker-compose.portainer.yml   # Portainer
├── docker-compose.postgres.yml    # PostgreSQL
├── docker-compose.app.yml         # Aplicação
├── Dockerfile                     # Imagem
├── deploy.sh                      # Deploy
├── backup.sh                      # Backup
├── monitor.sh                     # Monitor
├── PASSO_A_PASSO.md              # Guia
├── app.py                         # Aplicação
├── models/                        # Modelos
├── controllers/                   # Controllers
├── templates/                     # Templates
├── static/                        # Arquivos estáticos
├── utils/                         # Utilitários
├── migrations/                    # Migrações
├── traefik/data/                  # Dados Traefik
└── backups/                       # Backups
```

---

## ✅ **CHECKLIST DE VERIFICAÇÃO**

### **Após Deploy:**
- [ ] Traefik respondendo em `:8080`
- [ ] Portainer respondendo em `:9000`
- [ ] Aplicação respondendo em `:80` e `:5000`
- [ ] Login funcionando
- [ ] Criação de dossiê funcionando
- [ ] Upload de arquivos funcionando

### **Comandos de Verificação:**
```bash
curl -I http://**********:8080    # Traefik
curl -I http://**********:9000    # Portainer
curl -I http://**********          # Aplicação
curl -I http://**********:5000    # Aplicação direta
```

---

## 🆘 **TROUBLESHOOTING**

### **Problema Comum: Serviço não inicia**
```bash
# Verificar logs
docker service logs NOME_DO_SERVICO

# Verificar recursos
docker stats

# Restart forçado
docker service update --force NOME_DO_SERVICO
```

### **Problema: Aplicação não conecta no banco**
```bash
# Verificar PostgreSQL
docker service ps postgres_postgres

# Testar conexão
docker exec -it $(docker ps -q -f name=postgres_postgres) psql -U dossie -d dossie_escola -c "SELECT 1;"
```

### **Emergência: Parar tudo**
```bash
docker stack rm dossie traefik portainer postgres
docker system prune -f
```

---

## 🎉 **RESULTADO FINAL**

Após seguir o `PASSO_A_PASSO.md`, você terá:

✅ **Sistema completo** rodando em `http://**********`
✅ **Alta disponibilidade** com Docker Swarm
✅ **Gerenciamento visual** com Portainer
✅ **Proxy reverso** com Traefik
✅ **Banco PostgreSQL** persistente
✅ **Backup automático** configurado
✅ **Monitoramento** implementado
✅ **Acesso de qualquer computador** da rede local

**Sistema pronto para produção! 🚀**

---

## 📞 **SUPORTE**

1. **Leia primeiro**: `PASSO_A_PASSO.md`
2. **Execute**: `./monitor.sh` para diagnóstico
3. **Verifique logs**: `docker service logs dossie_dossie-app`
4. **Backup**: `./backup.sh` antes de mudanças

**Todos os arquivos estão prontos para uso! 🎯**
