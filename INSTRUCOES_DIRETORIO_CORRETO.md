# 📁 INSTRUÇÕES - <PERSON>ret<PERSON>rio Correto da Aplicação

## 🎯 **DIRETÓRIO ATUALIZADO**

A aplicação agora está configurada para rodar em:
```
/var/www/dossie_escolar
```

---

## 🔧 **ARQUIVOS ATUALIZADOS**

Os seguintes arquivos foram ajustados para o novo diretório:

✅ **`env-servidor-local`** - Adicionada variável APP_DIR
✅ **`docker-compose.app.yml`** - Build path atualizado
✅ **`deploy.sh`** - Caminhos corrigidos
✅ **`backup.sh`** - Diretório de backup atualizado
✅ **`docs/PASSO_A_PASSO.md`** - Instruções atualizadas
✅ **`README.md`** - Comandos corrigidos
✅ **`docs/README_DEPLOY.md`** - Paths atualizados
✅ **`setup-diretorio.sh`** - Novo script de configuração

---

## 🚀 **INSTRUÇÕES DE DEPLOY ATUALIZADAS**

### **PASSO 1: Preparar Servidor**
```bash
# Conectar no servidor
ssh usuario@**********

# Criar diretório (se necessário)
sudo mkdir -p /var/www/dossie_escolar
sudo chown -R $USER:$USER /var/www/dossie_escolar

# Instalar Docker (se necessário)
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# Inicializar Swarm
docker swarm init --advertise-addr **********
```

### **PASSO 2: Transferir Arquivos**
```bash
# Do seu computador local
rsync -avz /caminho/para/dossie_novo/ usuario@**********:/var/www/dossie_escolar/
```

### **PASSO 3: Configurar e Deploy**
```bash
# No servidor
cd /var/www/dossie_escolar

# Configurar ambiente
cp env-servidor-local .env
chmod +x *.sh

# Executar setup (opcional)
./setup-diretorio.sh

# Fazer deploy
./deploy.sh
```

### **PASSO 4: Acessar Sistema**
- **Sistema**: `http://**********`
- **Login**: `<EMAIL>` / `Admin@Local123`

---

## 📋 **VERIFICAÇÕES**

### **Verificar Estrutura:**
```bash
ls -la /var/www/dossie_escolar/
```

### **Verificar Permissões:**
```bash
ls -ld /var/www/dossie_escolar/
stat /var/www/dossie_escolar/
```

### **Verificar Scripts:**
```bash
ls -la /var/www/dossie_escolar/*.sh
```

---

## 🔧 **COMANDOS ÚTEIS**

### **Status do Sistema:**
```bash
cd /var/www/dossie_escolar
./monitor.sh
```

### **Backup:**
```bash
cd /var/www/dossie_escolar
./backup.sh
```

### **Logs:**
```bash
docker service logs dossie_dossie-app --tail 50
```

### **Restart:**
```bash
docker service update --force dossie_dossie-app
```

---

## 🗂️ **ESTRUTURA FINAL**

```
/var/www/dossie_escolar/
├── 📄 README.md
├── ⚙️ .env                         # Configurações
├── 🚀 deploy.sh                    # Deploy automático
├── 💾 backup.sh                    # Backup automático
├── 📊 monitor.sh                   # Monitoramento
├── 🔧 setup-diretorio.sh           # Configuração inicial
├── 🐳 Dockerfile
├── 📋 docker-compose.*.yml
├── 🌐 app.py
├── 🔧 manage.py
├── 📁 models/
├── 🎮 controllers/
├── 🎨 templates/
├── 📊 static/
├── 🔧 utils/
├── 📚 docs/
├── 🗄️ migrations/
├── 📁 traefik/data/
└── 💾 backups/
```

---

## ⚠️ **IMPORTANTE**

### **Permissões:**
- O diretório `/var/www/dossie_escolar` deve ter permissões corretas
- Scripts devem ter permissão de execução (`chmod +x *.sh`)
- Usuário deve ter acesso de escrita ao diretório

### **Docker:**
- Docker deve estar instalado e rodando
- Docker Swarm deve estar ativo
- Usuário deve estar no grupo docker

### **Rede:**
- Porta 80, 443, 5000, 8080, 9000 devem estar abertas
- IP ********** deve estar acessível na rede

---

## 🆘 **TROUBLESHOOTING**

### **Problema: Permissão negada**
```bash
sudo chown -R $USER:$USER /var/www/dossie_escolar
chmod +x /var/www/dossie_escolar/*.sh
```

### **Problema: Diretório não existe**
```bash
sudo mkdir -p /var/www/dossie_escolar
sudo chown -R $USER:$USER /var/www/dossie_escolar
```

### **Problema: Docker não funciona**
```bash
sudo systemctl start docker
sudo usermod -aG docker $USER
# Logout e login novamente
```

### **Problema: Swarm não ativo**
```bash
docker swarm init --advertise-addr **********
```

---

## ✅ **VERIFICAÇÃO FINAL**

Após o deploy, verificar:

- [ ] Sistema acessível em `http://**********`
- [ ] Login funcionando
- [ ] Portainer em `http://**********:9000`
- [ ] Traefik em `http://**********:8080`
- [ ] Todos os serviços rodando: `docker service ls`

---

## 🎉 **CONCLUSÃO**

Todos os arquivos foram atualizados para o diretório correto `/var/www/dossie_escolar`.

**O sistema está pronto para deploy no local correto! 🚀**

---

## 📞 **SUPORTE**

1. **Leia**: `docs/PASSO_A_PASSO.md` para instruções detalhadas
2. **Execute**: `./setup-diretorio.sh` para verificar configuração
3. **Execute**: `./monitor.sh` para diagnóstico
4. **Verifique**: Logs com `docker service logs dossie_dossie-app`

**Diretório correto configurado e pronto para uso! 📁✨**
