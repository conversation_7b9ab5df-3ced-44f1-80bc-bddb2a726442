#!/usr/bin/env python3
"""
Script para verificar status das migrações
"""

import os
import sys

# Definir variável de ambiente
os.environ['FLASK_APP'] = 'app.py'

# Importar Flask e configurar aplicação
from app import create_app
from flask_migrate import current, heads, show
from models import db

def check_migration_status():
    """Verificar status das migrações"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== Status das Migrações ===")
            
            # Verificar revisão atual
            current_rev = current()
            print(f"Revisão atual: {current_rev}")
            
            # Verificar heads (última migração disponível)
            heads_rev = heads()
            print(f"Última migração disponível: {heads_rev}")
            
            if current_rev != heads_rev:
                print("⚠️  Banco não está atualizado!")
                print("Execute: flask db upgrade")
            else:
                print("✓ Banco está atualizado")
            
            # Listar arquivos de migração
            migrations_dir = 'migrations/versions'
            if os.path.exists(migrations_dir):
                migrations = os.listdir(migrations_dir)
                migrations = [m for m in migrations if m.endswith('.py') and not m.startswith('__')]
                print(f"\nMigrações disponíveis ({len(migrations)}):")
                for migration in sorted(migrations):
                    print(f"  - {migration}")
            
        except Exception as e:
            print(f"❌ Erro: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    check_migration_status()
