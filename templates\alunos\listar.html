{% extends "base.html" %}

{% block title %}Lista de Alunos - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users me-2"></i>
                Lista de Alunos
            </h1>
            <a href="{{ url_for('novo_aluno') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Novo Aluno
            </a>
        </div>
    </div>
</div>

<!-- Filtros e Busca -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">Buscar por nome ou matrícula</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="Digite o nome ou matrícula...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                Buscar
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ url_for('listar_alunos') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Limpar
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Alunos -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if alunos.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Matrícula</th>
                                    <th>Nome</th>
                                    <th>Turma</th>
                                    <th>Ano Letivo</th>
                                    <th>Data Cadastro</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for aluno in alunos.items %}
                                <tr>
                                    <td>
                                        <strong>{{ aluno.matricula }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ aluno.nome }}</strong>
                                            {% if aluno.email %}
                                                <br><small class="text-muted">{{ aluno.email }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ aluno.turma or 'N/A' }}</span>
                                    </td>
                                    <td>{{ aluno.ano_letivo or 'N/A' }}</td>
                                    <td>
                                        <small>{{ aluno.data_cadastro.strftime('%d/%m/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('ver_aluno', id=aluno.id) }}" 
                                               class="btn btn-sm btn-outline-info" title="Ver detalhes">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-outline-warning" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    title="Excluir" onclick="confirmarExclusao({{ aluno.id }}, '{{ aluno.nome }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginação -->
                    {% if alunos.pages > 1 %}
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination justify-content-center">
                            {% if alunos.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('listar_alunos', page=alunos.prev_num, search=search) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in alunos.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != alunos.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('listar_alunos', page=page_num, search=search) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if alunos.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('listar_alunos', page=alunos.next_num, search=search) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum aluno encontrado</h5>
                        {% if search %}
                            <p class="text-muted">Tente ajustar os filtros de busca</p>
                            <a href="{{ url_for('listar_alunos') }}" class="btn btn-outline-primary">
                                <i class="fas fa-times me-2"></i>
                                Limpar filtros
                            </a>
                        {% else %}
                            <p class="text-muted">Comece cadastrando o primeiro aluno</p>
                            <a href="{{ url_for('novo_aluno') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Cadastrar Aluno
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmarExclusao(id, nome) {
    if (confirm('Tem certeza que deseja excluir o aluno "' + nome + '"?\n\nEsta ação não pode ser desfeita e todos os dossiês relacionados também serão excluídos.')) {
        // Implementar exclusão via AJAX ou form
        alert('Funcionalidade de exclusão será implementada');
    }
}
</script>
{% endblock %}
