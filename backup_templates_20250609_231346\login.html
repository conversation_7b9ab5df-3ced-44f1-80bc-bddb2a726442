{% extends "base.html" %}

{% block title %}Login - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Login
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            Email
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="senha" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            <PERSON>ha
                        </label>
                        <input type="password" class="form-control" id="senha" name="senha" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Entrar
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center text-muted">
                <small>
                    <i class="fas fa-info-circle me-1"></i>
                    Usuário padrão: <EMAIL> / admin123
                </small>
            </div>
        </div>
        
        <div class="text-center mt-3">
            <a href="{{ url_for('index') }}" class="text-decoration-none">
                <i class="fas fa-arrow-left me-1"></i>
                Voltar ao início
            </a>
        </div>
    </div>
</div>
{% endblock %}
