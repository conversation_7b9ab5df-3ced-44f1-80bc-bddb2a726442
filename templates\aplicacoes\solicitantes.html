<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitantes - Sistema Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-secondary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-graduation-cap me-2"></i>Sistema Modular
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>
                    <i class="fas fa-user-friends me-2"></i>
                    Aplicação SOLICITANTES
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Solicitantes</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">Gestão de Solicitantes</h5>
                    </div>
                    <div class="card-body text-center py-5">
                        <i class="fas fa-user-friends fa-4x text-secondary mb-4"></i>
                        <h4>Aplicação SOLICITANTES</h4>
                        <p class="lead">Gestão completa de solicitantes</p>
                        <p>Nome, endereço, celular, cidade, CPF, email, parentesco, data de nascimento, tipo de solicitação, status</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-plus fa-2x text-secondary mb-2"></i>
                                        <h6>Novo Solicitante</h6>
                                        <small>Cadastrar solicitante</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-search fa-2x text-secondary mb-2"></i>
                                        <h6>Buscar</h6>
                                        <small>Busca por CPF/Nome</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-heart fa-2x text-secondary mb-2"></i>
                                        <h6>Parentesco</h6>
                                        <small>Controle de parentesco</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-history fa-2x text-secondary mb-2"></i>
                                        <h6>Histórico</h6>
                                        <small>Histórico de solicitações</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-secondary">
                    <h6><i class="fas fa-info-circle me-2"></i>Aplicação SOLICITANTES</h6>
                    <p class="mb-0">
                        Aplicação modular para gestão de solicitantes conforme CLAUDE.md.
                        Controla dados pessoais, parentesco e histórico de solicitações.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
