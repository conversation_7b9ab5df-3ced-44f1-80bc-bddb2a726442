# 🏫 Sistema de Controle de Dossiê Escolar

Sistema web completo para gerenciamento de dossiês escolares desenvolvido em Flask com Docker Swarm, Traefik e Portainer.

## 🚀 **Deploy Rápido - Servidor Local**

### **Pré-requisitos:**
- Servidor com IP `**********`
- <PERSON>er instalado
- Docker Swarm ativo

### **Instalação em 3 comandos:**
```bash
# 1. Transferir arquivos para o servidor
rsync -avz /caminho/local/ usuario@**********:/var/www/dossie_escolar/

# 2. No servidor, configurar ambiente
cd /var/www/dossie_escolar
cp env-servidor-local .env
chmod +x *.sh

# 3. Executar deploy
./deploy.sh
```

### **Acessar Sistema:**
- **🌐 Sistema**: `http://**********`
- **👤 Login**: `<EMAIL>` / `Admin@Local123`
- **📊 Portainer**: `http://**********:9000`
- **🔀 Traefik**: `http://**********:8080`

---

## 📚 **Documentação Completa**

Toda a documentação está organizada na pasta **`docs/`**:

### **🚀 Deploy e Instalação:**
- **[PASSO_A_PASSO.md](docs/PASSO_A_PASSO.md)** - Guia completo de instalação
- **[DEPLOY_SERVIDOR_LOCAL.md](docs/DEPLOY_SERVIDOR_LOCAL.md)** - Deploy detalhado
- **[README_DEPLOY.md](docs/README_DEPLOY.md)** - Resumo e comandos úteis

### **🐳 Docker e Infraestrutura:**
- **[DOCKER_SWARM_DEPLOY_GUIDE.md](docs/DOCKER_SWARM_DEPLOY_GUIDE.md)** - Guia Docker Swarm
- **[docker-compose-files.md](docs/docker-compose-files.md)** - Arquivos Docker Compose
- **[DEPLOY_HOSTINGER_VPS.md](docs/DEPLOY_HOSTINGER_VPS.md)** - Deploy em VPS

### **🔧 Configuração e Manutenção:**
- **[GUIA_POSTGRESQL.md](docs/GUIA_POSTGRESQL.md)** - Configuração PostgreSQL
- **[GUIA_MIGRAÇÕES_E_ADMIN.md](docs/GUIA_MIGRAÇÕES_E_ADMIN.md)** - Migrações e Admin
- **[SECURITY_IMPROVEMENTS.md](docs/SECURITY_IMPROVEMENTS.md)** - Melhorias de segurança

### **📋 Análises e Correções:**
- **[ARQUIVOS_PYTHON_ANALISE.md](docs/ARQUIVOS_PYTHON_ANALISE.md)** - Análise de arquivos
- **[DOSSIE_DUPLICACAO_FIX.md](docs/DOSSIE_DUPLICACAO_FIX.md)** - Correção duplicação
- **[UNBOUNDLOCALERROR_FIX.md](docs/UNBOUNDLOCALERROR_FIX.md)** - Correções de bugs

---

## 🔧 **Comandos Úteis**

### **Monitoramento:**
```bash
./monitor.sh              # Status completo do sistema
docker service ls         # Lista serviços
docker stack ls           # Lista stacks
```

### **Logs:**
```bash
docker service logs dossie_dossie-app --tail 50
docker service logs postgres_postgres --tail 20
```

### **Backup:**
```bash
./backup.sh               # Backup completo
```

### **Restart:**
```bash
docker service update --force dossie_dossie-app
```

---

## 🏗️ **Arquitetura**

- **🐳 Docker Swarm** - Orquestração de containers
- **🔀 Traefik** - Reverse proxy e load balancer
- **📊 Portainer** - Interface de gerenciamento
- **🐘 PostgreSQL** - Banco de dados
- **🌐 Flask** - Framework web Python
- **📱 Bootstrap 5** - Interface responsiva

---

## 📦 **Estrutura do Projeto**

```
/var/www/dossie_escolar/
├── 📄 README.md                    # Este arquivo
├── 🚀 deploy.sh                    # Script de deploy
├── 💾 backup.sh                    # Script de backup
├── 📊 monitor.sh                   # Script de monitoramento
├── 🐳 Dockerfile                   # Imagem Docker
├── ⚙️ docker-compose.*.yml         # Configurações Docker
├── 🌐 app.py                       # Aplicação principal
├── 🔧 manage.py                    # Comandos de gerenciamento
├── 📁 models/                      # Modelos do banco
├── 🎮 controllers/                 # Controllers
├── 🎨 templates/                   # Templates HTML
├── 📊 static/                      # Arquivos estáticos
├── 🔧 utils/                       # Utilitários
├── 📚 docs/                        # Documentação completa
├── 🗄️ migrations/                  # Migrações do banco
├── 📁 traefik/data/                # Dados do Traefik
└── 💾 backups/                     # Backups do sistema
```

---

## ✅ **Funcionalidades**

### **👥 Gestão de Usuários:**
- Perfis: Admin Geral, Admin Escolar, Funcionário
- Controle de permissões por escola
- Sistema de autenticação seguro

### **📋 Controle de Dossiês:**
- Cadastro completo de dossiês
- Upload de documentos
- Movimentações e histórico
- Busca avançada

### **🏫 Multi-tenant:**
- Suporte a múltiplas escolas
- Isolamento total de dados
- Administração centralizada

### **🔒 Segurança:**
- Rate limiting
- Logs de auditoria
- Validação de dados
- Backup automático

---

## 🆘 **Suporte**

### **Problemas Comuns:**
1. **Aplicação não responde**: Execute `./monitor.sh`
2. **Erro de banco**: Verifique logs com `docker service logs postgres_postgres`
3. **Deploy falha**: Verifique se Docker Swarm está ativo

### **Comandos de Emergência:**
```bash
# Parar tudo
docker stack rm dossie traefik portainer postgres

# Limpar sistema
docker system prune -f

# Reiniciar deploy
./deploy.sh
```

---

## 📞 **Contato**

Para dúvidas ou suporte:
1. **Consulte** a documentação em `docs/`
2. **Execute** `./monitor.sh` para diagnóstico
3. **Verifique** logs dos serviços

---

**🎉 Sistema pronto para produção com infraestrutura profissional!**
