{% extends "base.html" %}

{% block title %}Dashboard - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            Dashboard
        </h1>
    </div>
</div>

<!-- Cards de Estatísticas -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_alunos }}</h4>
                        <p class="card-text">Total de Alunos</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('listar_alunos') }}" class="text-white text-decoration-none">
                    Ver todos <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_dossies }}</h4>
                        <p class="card-text">Total de Dossiês</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="#" class="text-white text-decoration-none">
                    Ver todos <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_documentos }}</h4>
                        <p class="card-text">Total de Documentos</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="#" class="text-white text-decoration-none">
                    Ver todos <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">0</h4>
                        <p class="card-text">Pendências</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="#" class="text-white text-decoration-none">
                    Ver todas <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Seção de Alunos Recentes e Dossiês Recentes -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    Alunos Recentes
                </h5>
            </div>
            <div class="card-body">
                {% if alunos_recentes %}
                    <div class="list-group list-group-flush">
                        {% for aluno in alunos_recentes %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ aluno.nome }}</h6>
                                <small class="text-muted">Matrícula: {{ aluno.matricula }} | Turma: {{ aluno.turma }}</small>
                            </div>
                            <a href="{{ url_for('ver_aluno', id=aluno.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Nenhum aluno cadastrado ainda.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('listar_alunos') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Ver todos os alunos
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    Dossiês Recentes
                </h5>
            </div>
            <div class="card-body">
                {% if dossies_recentes %}
                    <div class="list-group list-group-flush">
                        {% for dossie in dossies_recentes %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ dossie.titulo }}</h6>
                                    <small class="text-muted">{{ dossie.aluno.nome }}</small>
                                    <br>
                                    <span class="badge bg-{{ 'danger' if dossie.prioridade == 'alta' else 'warning' if dossie.prioridade == 'media' else 'success' }}">
                                        {{ dossie.prioridade.title() }}
                                    </span>
                                    <span class="badge bg-secondary">{{ dossie.tipo.title() }}</span>
                                </div>
                                <small class="text-muted">{{ dossie.data_criacao.strftime('%d/%m/%Y') }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Nenhum dossiê criado ainda.</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="#" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Ver todos os dossiês
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('novo_aluno') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            Novo Aluno
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-folder-plus me-2"></i>
                            Novo Dossiê
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="fas fa-file-upload me-2"></i>
                            Upload Documento
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            Relatórios
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
