<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acesso negado - Sistema de Controle de Dossiê Escolar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            text-align: center;
            color: white;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 0;
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .btn-home {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: white;
            color: #ffc107;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .floating-icon {
            animation: pulse 2s ease-in-out infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="error-container">
                    <div class="floating-icon mb-4">
                        <i class="fas fa-shield-alt fa-4x"></i>
                    </div>
                    
                    <h1 class="error-code">403</h1>
                    
                    <p class="error-message">
                        Acesso negado
                    </p>
                    
                    <p class="mb-4 opacity-75">
                        Você não tem permissão para acessar esta página ou recurso.
                    </p>
                    
                    <div class="alert alert-light text-dark mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Possíveis motivos:</strong>
                        <ul class="mb-0 mt-2 text-start">
                            <li>Seu perfil de usuário não tem as permissões necessárias</li>
                            <li>Você está tentando acessar dados de outra escola</li>
                            <li>Sua sessão pode ter expirado</li>
                            <li>Esta funcionalidade requer privilégios administrativos</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        {% if session.user_id %}
                            <a href="{{ url_for('dashboard') }}" class="btn-home">
                                <i class="fas fa-home me-2"></i>
                                Voltar ao Dashboard
                            </a>
                            <button onclick="history.back()" class="btn-home" style="border: none;">
                                <i class="fas fa-arrow-left me-2"></i>
                                Página Anterior
                            </button>
                            <a href="{{ url_for('auth.logout') }}" class="btn-home">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Fazer Logout
                            </a>
                        {% else %}
                            <a href="{{ url_for('auth.login') }}" class="btn-home">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Fazer Login
                            </a>
                        {% endif %}
                    </div>
                    
                    <div class="mt-5">
                        <small class="opacity-75">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Sistema de Controle de Dossiê Escolar
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
