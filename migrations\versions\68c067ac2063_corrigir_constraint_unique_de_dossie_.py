"""Corrigir constraint unique de dossie para ser por escola

Revision ID: 68c067ac2063
Revises: c0820d235740
Create Date: 2025-06-11 01:21:51.354677

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '68c067ac2063'
down_revision = 'c0820d235740'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('dossies', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('dossies_n_dossie_key'), type_='unique')
        batch_op.create_unique_constraint('unique_dossie_per_escola', ['n_dossie', 'id_escola'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('dossies', schema=None) as batch_op:
        batch_op.drop_constraint('unique_dossie_per_escola', type_='unique')
        batch_op.create_unique_constraint(batch_op.f('dossies_n_dossie_key'), ['n_dossie'], postgresql_nulls_not_distinct=False)

    # ### end Alembic commands ###
