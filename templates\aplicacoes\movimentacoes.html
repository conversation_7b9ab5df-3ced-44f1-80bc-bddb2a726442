<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Movimentações - Sistema Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-warning">
        <div class="container-fluid">
            <a class="navbar-brand text-dark" href="{{ url_for('dashboard') }}">
                <i class="fas fa-graduation-cap me-2"></i>Sistema Modular
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-dark" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>
                    <i class="fas fa-exchange-alt me-2"></i>
                    Aplicação MOVIMENTAÇÕES
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Movimentações</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">Controle de Movimentações</h5>
                    </div>
                    <div class="card-body text-center py-5">
                        <i class="fas fa-exchange-alt fa-4x text-warning mb-4"></i>
                        <h4>Aplicação MOVIMENTAÇÕES</h4>
                        <p class="lead">Controle completo de movimentações de dossiês</p>
                        <p>Escola, solicitante, descrição, data de solicitação, data de devolução, status, dossiê, responsável, tipo de documentação, observação</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-plus fa-2x text-warning mb-2"></i>
                                        <h6>Nova Movimentação</h6>
                                        <small>Registrar movimentação</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h6>Pendentes</h6>
                                        <small>Movimentações pendentes</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check fa-2x text-warning mb-2"></i>
                                        <h6>Aprovadas</h6>
                                        <small>Movimentações aprovadas</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-undo fa-2x text-warning mb-2"></i>
                                        <h6>Devoluções</h6>
                                        <small>Controle de devoluções</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-info-circle me-2"></i>Aplicação MOVIMENTAÇÕES</h6>
                    <p class="mb-0">
                        Aplicação modular para controle de movimentações conforme CLAUDE.md.
                        Gerencia solicitações, aprovações, empréstimos e devoluções de dossiês.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
