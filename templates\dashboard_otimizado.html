{% extends "base.html" %}

{% block title %}Dashboard - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block content %}
<!-- Header Simples e Rápido -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="mb-2 text-white">
                            <i class="fas fa-tachometer-alt me-3"></i>
                            Dashboard do Sistema
                        </h1>
                        <p class="mb-0 text-white-75">
                            Visão geral rápida e eficiente do sistema
                        </p>
                    </div>
                    <div class="col-lg-4 text-end">
                        <div class="h4 text-white mb-0">{{ current_date.strftime('%d/%m/%Y') }}</div>
                        <div class="text-white-75">{{ current_date.strftime('%H:%M') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPIs Otimizados -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total de Dossiês
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_dossies }}</div>
                        <div class="mt-2 mb-0 text-muted text-xs">
                            <span class="text-success mr-2">
                                <i class="fas fa-arrow-up"></i> {{ stats.dossies_mes_atual }}
                            </span>
                            <span>este mês</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Movimentações
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_movimentacoes }}</div>
                        <div class="mt-2 mb-0 text-muted text-xs">
                            <span class="text-success mr-2">
                                <i class="fas fa-arrow-up"></i> {{ stats.movimentacoes_mes_atual }}
                            </span>
                            <span>este mês</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Usuários Ativos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.usuarios_ativos }}</div>
                        <div class="mt-2 mb-0 text-muted text-xs">
                            <span class="text-info mr-2">
                                <i class="fas fa-users"></i> {{ stats.total_usuarios }}
                            </span>
                            <span>total</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pendências
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.movimentacoes_pendentes }}</div>
                        <div class="mt-2 mb-0 text-muted text-xs">
                            <span class="text-warning mr-2">
                                <i class="fas fa-clock"></i> Aguardando
                            </span>
                            <span>processamento</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos Simples -->
<div class="row mb-4">
    <!-- Gráfico Principal -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area me-2"></i>Evolução de Dossiês (Últimos 3 Meses)
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="dossiesChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Pizza -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>Tipos de Movimentação
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="movimentacoesChart" width="100%" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Acesso Rápido -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-rocket me-2"></i>Acesso Rápido
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('dossie.novo') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-plus me-2"></i>Novo Dossiê
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('movimentacao.nova') }}" class="btn btn-success btn-block">
                            <i class="fas fa-exchange-alt me-2"></i>Nova Movimentação
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/solicitantes/novo" class="btn btn-info btn-block">
                            <i class="fas fa-user-plus me-2"></i>Novo Solicitante
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('configuracao.index') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-cog me-2"></i>Configurações
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Informações do Sistema -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-muted">Usuário Logado</h6>
                        <p class="mb-1"><strong>{{ usuario.nome }}</strong></p>
                        <p class="mb-1">Perfil: <span class="badge bg-primary">{{ usuario.perfil_obj.perfil if usuario.perfil_obj else 'N/A' }}</span></p>
                        <p class="mb-0">Escola: {{ usuario.escola.nome if usuario.escola else 'N/A' }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Sistema</h6>
                        <p class="mb-1">Versão: <strong>2.0.0 Otimizada</strong></p>
                        <p class="mb-1">Status: <span class="badge bg-success">Online</span></p>
                        <p class="mb-0">Performance: <span class="badge bg-info">Otimizada</span></p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">Última Atualização</h6>
                        <p class="mb-1">Data: <strong>{{ current_date.strftime('%d/%m/%Y') }}</strong></p>
                        <p class="mb-1">Hora: <strong>{{ current_date.strftime('%H:%M:%S') }}</strong></p>
                        <p class="mb-0">Carregamento: <span class="badge bg-success">Rápido</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts Otimizados -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dados simplificados para performance
const dossiesPorMes = {{ stats.dossies_por_mes | tojson }};
const movimentacoesPorTipo = {{ stats.movimentacoes_por_tipo | tojson }};

// Gráfico de linha otimizado
const ctxDossies = document.getElementById('dossiesChart').getContext('2d');
const dossiesChart = new Chart(ctxDossies, {
    type: 'line',
    data: {
        labels: dossiesPorMes.map(item => item.mes),
        datasets: [{
            label: 'Dossiês Criados',
            data: dossiesPorMes.map(item => item.count),
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        },
        animation: {
            duration: 1000
        }
    }
});

// Gráfico de pizza otimizado
const ctxMovimentacoes = document.getElementById('movimentacoesChart').getContext('2d');
const movimentacoesChart = new Chart(ctxMovimentacoes, {
    type: 'doughnut',
    data: {
        labels: movimentacoesPorTipo.map(item => item.tipo),
        datasets: [{
            data: movimentacoesPorTipo.map(item => item.count),
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b'
            ],
            borderWidth: 2,
            borderColor: '#ffffff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true
                }
            }
        },
        animation: {
            duration: 1000
        }
    }
});

// Atualização menos frequente para melhor performance
setInterval(function() {
    console.log('Sistema funcionando normalmente');
}, 600000); // 10 minutos
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

.btn-block {
    width: 100%;
}

.chart-area {
    position: relative;
    height: 320px;
}

.chart-pie {
    position: relative;
    height: 245px;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}
</style>
{% endblock %}
