{% extends "admin/base.html" %}

{% block title %}Histórico - {{ config.nome_exibicao }}{% endblock %}
{% block header %}Histórico de Configuração{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('configuracao.index') }}">Configurações</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('configuracao.categoria', categoria=config.categoria.value) }}">{{ config.categoria.value.replace('_', ' ').title() }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('configuracao.editar', config_id=config.id) }}">{{ config.nome_exibicao }}</a></li>
                <li class="breadcrumb-item active">Histórico</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>Histórico de Alterações
                </h5>
                <div>
                    <span class="badge bg-info">{{ config.chave }}</span>
                </div>
            </div>
            <div class="card-body">
                <!-- Informações da Configuração -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h6>{{ config.nome_exibicao }}</h6>
                        <p class="text-muted mb-2">{{ config.descricao or 'Sem descrição disponível' }}</p>
                        <div>
                            <span class="badge bg-primary me-1">{{ config.categoria.value }}</span>
                            <span class="badge bg-info me-1">{{ config.tipo.value }}</span>
                            <span class="badge bg-secondary">{{ config.escopo.value }}</span>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="mb-2">
                            <strong>Valor Atual:</strong>
                        </div>
                        <div class="p-2 bg-light rounded">
                            {% if config.tipo.value == 'boolean' %}
                                <span class="badge bg-{{ 'success' if config.valor_tipado else 'secondary' }} fs-6">
                                    {{ 'Ativo' if config.valor_tipado else 'Inativo' }}
                                </span>
                            {% else %}
                                <code>{{ config.valor }}</code>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Timeline de Histórico -->
                {% if historico %}
                    <div class="timeline">
                        {% for item in historico %}
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="mb-1">
                                                    <i class="fas fa-user me-1"></i>
                                                    {{ item.usuario.nome if item.usuario else 'Sistema' }}
                                                </h6>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {{ item.data_mudanca.strftime('%d/%m/%Y às %H:%M:%S') }}
                                                    {% if item.ip_address %}
                                                        | <i class="fas fa-globe me-1"></i>{{ item.ip_address }}
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label small text-muted">Valor Anterior:</label>
                                                <div class="p-2 bg-danger bg-opacity-10 rounded">
                                                    {% if config.tipo.value == 'boolean' %}
                                                        <span class="badge bg-{{ 'success' if item.valor_anterior == 'true' else 'secondary' }}">
                                                            {{ 'Ativo' if item.valor_anterior == 'true' else 'Inativo' }}
                                                        </span>
                                                    {% elif config.tipo.value == 'json' %}
                                                        <pre class="mb-0 small"><code>{{ item.valor_anterior }}</code></pre>
                                                    {% else %}
                                                        <code class="text-danger">{{ item.valor_anterior or 'Vazio' }}</code>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label small text-muted">Valor Novo:</label>
                                                <div class="p-2 bg-success bg-opacity-10 rounded">
                                                    {% if config.tipo.value == 'boolean' %}
                                                        <span class="badge bg-{{ 'success' if item.valor_novo == 'true' else 'secondary' }}">
                                                            {{ 'Ativo' if item.valor_novo == 'true' else 'Inativo' }}
                                                        </span>
                                                    {% elif config.tipo.value == 'json' %}
                                                        <pre class="mb-0 small"><code>{{ item.valor_novo }}</code></pre>
                                                    {% else %}
                                                        <code class="text-success">{{ item.valor_novo or 'Vazio' }}</code>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        {% if item.motivo %}
                                        <div class="mt-3">
                                            <label class="form-label small text-muted">Motivo:</label>
                                            <div class="p-2 bg-light rounded">
                                                <small>{{ item.motivo }}</small>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum histórico encontrado</h5>
                        <p class="text-muted">Esta configuração ainda não foi alterada.</p>
                    </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('configuracao.editar', config_id=config.id) }}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Voltar à Edição
                    </a>
                    <div>
                        <small class="text-muted">
                            Mostrando últimas {{ historico|length }} alterações
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    margin-left: 20px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}
</style>
{% endblock %}
