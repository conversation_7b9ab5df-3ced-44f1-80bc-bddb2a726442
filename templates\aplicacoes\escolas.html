<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Escolas - Sistema Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-graduation-cap me-2"></i>Sistema Modular
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>
                    <i class="fas fa-school me-2"></i>
                    Aplicação ESCOLAS
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Escolas</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Lista de Escolas</h5>
                    </div>
                    <div class="card-body">
                        {% if escolas %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>UF</th>
                                            <th>CNPJ</th>
                                            <th>INEP</th>
                                            <th>Diretor</th>
                                            <th>Situação</th>
                                            <th>Data Cadastro</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for escola in escolas %}
                                        <tr>
                                            <td>{{ escola.nome }}</td>
                                            <td><span class="badge bg-secondary">{{ escola.uf }}</span></td>
                                            <td><code>{{ escola.cnpj or 'N/A' }}</code></td>
                                            <td><code>{{ escola.inep or 'N/A' }}</code></td>
                                            <td>{{ escola.diretor or 'N/A' }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if escola.situacao == 'ativa' else 'danger' }}">
                                                    {{ escola.situacao.title() }}
                                                </span>
                                            </td>
                                            <td>{{ escola.data_cadastro.strftime('%d/%m/%Y') }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-school fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Nenhuma escola cadastrada</h5>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Aplicação ESCOLAS</h6>
                    <p class="mb-0">
                        Esta é a aplicação modular para gestão de escolas. 
                        Possui seus próprios models, routes e templates conforme arquitetura CLAUDE.md.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
