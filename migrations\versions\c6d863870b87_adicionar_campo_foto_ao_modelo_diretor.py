"""Adicionar campo foto ao modelo Diretor

Revision ID: c6d863870b87
Revises: e7a02b307733
Create Date: 2025-06-10 18:23:13.130118

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c6d863870b87'
down_revision = 'e7a02b307733'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('diretores', schema=None) as batch_op:
        batch_op.add_column(sa.Column('foto', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('diretores', schema=None) as batch_op:
        batch_op.drop_column('foto')

    # ### end Alembic commands ###
