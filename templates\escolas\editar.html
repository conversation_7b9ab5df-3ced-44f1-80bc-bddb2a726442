{% extends "base.html" %}

{% block title %}Editar {{ escola.nome }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit me-2"></i>
                Editar Escola
            </h1>
            <div>
                <a href="{{ url_for('escola.ver', id=escola.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Editando: {{ escola.nome }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-8">
                            <label for="nome" class="form-label">Nome da Escola *</label>
                            <input type="text" class="form-control" id="nome" name="nome" value="{{ escola.nome }}" required>
                        </div>
                        <div class="col-md-4">
                            <label for="uf" class="form-label">UF *</label>
                            <select class="form-select" id="uf" name="uf" required>
                                <option value="">Selecione...</option>
                                {% for uf_option in ['AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'] %}
                                    <option value="{{ uf_option }}" {{ 'selected' if escola.uf == uf_option else '' }}>{{ uf_option }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <label for="endereco" class="form-label">Endereço</label>
                            <input type="text" class="form-control" id="endereco" name="endereco" value="{{ escola.endereco or '' }}">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label for="cnpj" class="form-label">CNPJ</label>
                            <input type="text" class="form-control" id="cnpj" name="cnpj" value="{{ escola.cnpj or '' }}" placeholder="00.000.000/0000-00">
                        </div>
                        <div class="col-md-4">
                            <label for="inep" class="form-label">Código INEP</label>
                            <input type="text" class="form-control" id="inep" name="inep" value="{{ escola.inep or '' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="situacao" class="form-label">Situação</label>
                            <select class="form-select" id="situacao" name="situacao">
                                <option value="ativa" {{ 'selected' if escola.situacao == 'ativa' else '' }}>Ativa</option>
                                <option value="inativa" {{ 'selected' if escola.situacao == 'inativa' else '' }}>Inativa</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ escola.email or '' }}">
                        </div>
                        <div class="col-md-6">
                            <label for="telefone" class="form-label">Telefone</label>
                            <input type="text" class="form-control" id="telefone" name="telefone" value="{{ escola.telefone or '' }}" placeholder="(00) 0000-0000">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="diretor" class="form-label">Diretor</label>
                            <input type="text" class="form-control" id="diretor" name="diretor" value="{{ escola.diretor or '' }}">
                        </div>
                        <div class="col-md-6">
                            <label for="vice_diretor" class="form-label">Vice-Diretor</label>
                            <input type="text" class="form-control" id="vice_diretor" name="vice_diretor" value="{{ escola.vice_diretor or '' }}">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="id_cidade" class="form-label">Cidade</label>
                            <select class="form-select" id="id_cidade" name="id_cidade">
                                <option value="">Selecione...</option>
                                {% for cidade in cidades %}
                                    <option value="{{ cidade.id_cidade }}" {{ 'selected' if escola.id_cidade == cidade.id_cidade else '' }}>
                                        {{ cidade.nome }} - {{ cidade.uf }}
                                    </option>
                                {% endfor %}
                            </select>
                            {% if not cidades %}
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Nenhuma cidade cadastrada. <a href="{{ url_for('cidade.nova') }}" target="_blank">Cadastrar cidade</a>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="diretor_id" class="form-label">Diretor (Usuário)</label>
                            <select class="form-select" id="diretor_id" name="diretor_id">
                                <option value="">Selecione...</option>
                                {% for usuario in usuarios %}
                                    <option value="{{ usuario.id }}" {{ 'selected' if escola.diretor_id == usuario.id else '' }}>
                                        {{ usuario.nome }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="observacoes" class="form-label">Observações</label>
                            <textarea class="form-control" id="observacoes" name="observacoes" rows="3">{{ escola.observacoes or '' }}</textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('escola.ver', id=escola.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>Salvar Alterações
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Máscara para CNPJ
    document.getElementById('cnpj').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        value = value.replace(/^(\d{2})(\d)/, '$1.$2');
        value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
        value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
        value = value.replace(/(\d{4})(\d)/, '$1-$2');
        e.target.value = value;
    });

    // Máscara para telefone
    document.getElementById('telefone').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        value = value.replace(/^(\d{2})(\d)/, '($1) $2');
        value = value.replace(/(\d{4})(\d)/, '$1-$2');
        e.target.value = value;
    });
</script>
{% endblock %}
