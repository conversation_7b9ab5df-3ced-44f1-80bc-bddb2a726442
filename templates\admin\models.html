{% extends "admin/base.html" %}

{% block title %}Modelos{% endblock %}
{% block header %}Gerenciar Modelos{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <p class="text-muted mb-4">
            Gere<PERSON><PERSON> todos os dados do sistema através dos modelos abaixo.
        </p>
    </div>
</div>

<div class="row">
    {% for model in models %}
    <div class="col-md-4 col-lg-3 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="{{ model.icon }} fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">{{ model.verbose_name }}</h5>
                <p class="card-text">
                    <span class="badge bg-secondary">{{ model.count }} registro(s)</span>
                </p>
                <a href="{{ model.url }}" class="btn btn-primary">
                    <i class="fas fa-list me-2"></i>Gerenciar
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Ações Rápidas -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>Ações Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('admin.backup') }}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-download me-2"></i>Fazer Backup
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('admin.system_info') }}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-info-circle me-2"></i>Info Sistema
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('permissao.perfis') }}" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-shield-alt me-2"></i>Permissões
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-file-alt me-2"></i>Ver Logs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
