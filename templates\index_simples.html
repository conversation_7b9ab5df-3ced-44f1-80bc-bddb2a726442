<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Controle de Dossiê Escolar - Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="jumbotron bg-primary text-white text-center py-5 rounded mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-graduation-cap me-3"></i>
                        Sistema de Controle de Dossiê Escolar
                    </h1>
                    <p class="lead">Arquitetura Modular - Conforme CLAUDE.md</p>
                    <hr class="my-4 bg-white">
                    <p>Sistema em inicialização...</p>
                    <a class="btn btn-light btn-lg" href="/auth/login">
                        <i class="fas fa-sign-in-alt me-2"></i>Fazer Login
                    </a>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-cubes text-primary me-2"></i>
                                    Arquitetura Modular
                                </h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação AUTH - Autenticação</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação CORE - Entidades auxiliares</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação ESCOLAS - Gestão de escolas</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação USUÁRIOS - Gestão de usuários</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação DOSSIÊS - Gestão de dossiês</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação MOVIMENTAÇÕES - Controle de movimentações</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação SOLICITANTES - Gestão de solicitantes</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação LOGS - Logs e auditoria</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Aplicação RELATÓRIOS - Relatórios do sistema</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-database text-info me-2"></i>
                                    Entidades Implementadas
                                </h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-table text-info me-2"></i>Cidades</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Perfis</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Configurações por Escola</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Escolas</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Usuários</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Dossiês</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Movimentações</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Solicitantes</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Logs de Auditoria</li>
                                    <li><i class="fas fa-table text-info me-2"></i>Logs do Sistema</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-info-circle me-2"></i>Status do Sistema</h5>
                    <p class="mb-0">
                        Sistema implementado com arquitetura modular conforme especificação CLAUDE.md.
                        Cada entidade possui sua própria aplicação com models, routes e templates específicos.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
