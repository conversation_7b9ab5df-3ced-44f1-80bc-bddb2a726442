# models/__init__.py
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

# Importar todos os modelos para registro
from .perfil import Perfil
from .permissao import Permissao, PerfilPermissao
from .cidade import Cidade
from .escola import Escola, ConfiguracaoEscola, CONFIGURACOES_PADRAO
from .usuario import Usuario
from .diretor import Diretor
from .dossie import <PERSON><PERSON>
from .movimentacao import Movimentacao
from .anexo import Anexo
from .solicitante import Solicitante
from .log_auditoria import LogAuditoria, LogSistema
from .configuracao_avancada import ConfiguracaoSistema, HistoricoConfiguracao

__all__ = ['db', 'Perfil', 'Permissao', 'PerfilPermissao', 'Cidade', 'Escola', 'ConfiguracaoEscola', 'CONFIGURACOES_PADRAO',
           '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>or', '<PERSON>ssie', '<PERSON>vi<PERSON>aca<PERSON>', 'Anexo', 'Solicitante', 'LogAudi<PERSON>', 'LogSistema',
           'ConfiguracaoSistema', 'HistoricoConfiguracao']
