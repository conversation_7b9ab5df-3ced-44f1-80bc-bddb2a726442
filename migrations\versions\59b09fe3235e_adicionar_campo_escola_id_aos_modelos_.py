"""Adicionar campo escola_id aos modelos Solicitante e Diretor

Revision ID: 59b09fe3235e
Revises: ec4101429d0a
Create Date: 2025-06-10 22:45:44.097981

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '59b09fe3235e'
down_revision = 'ec4101429d0a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('diretores', schema=None) as batch_op:
        batch_op.alter_column('escola_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.create_foreign_key(None, 'escolas', ['escola_id'], ['id'])

    with op.batch_alter_table('solicitantes', schema=None) as batch_op:
        batch_op.alter_column('escola_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.create_foreign_key(None, 'escolas', ['escola_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('solicitantes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.alter_column('escola_id',
               existing_type=sa.INTEGER(),
               nullable=True)

    with op.batch_alter_table('diretores', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.alter_column('escola_id',
               existing_type=sa.INTEGER(),
               nullable=True)

    # ### end Alembic commands ###
