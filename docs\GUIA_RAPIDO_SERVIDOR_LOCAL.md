# ⚡ GUIA RÁPIDO - Deploy Servidor Local **********

## 🚀 **INSTALAÇÃO EM 3 PASSOS**

### **PASSO 1: Executar Instalação Automática**
```bash
# No servidor **********
wget https://raw.githubusercontent.com/SEU_REPO/install-servidor-local.sh
chmod +x install-servidor-local.sh
./install-servidor-local.sh
```

### **PASSO 2: Copiar <PERSON>ódigo da Aplicação**
```bash
# Do seu computador para o servidor
rsync -avz --progress /caminho/para/dossie_novo/ usuario@**********:/opt/dossie-app/app/

# Ou via SCP
scp -r /caminho/para/dossie_novo/* usuario@**********:/opt/dossie-app/app/
```

### **PASSO 3: Fazer Deploy**
```bash
# No servidor
cd /opt/dossie-app
./deploy-local.sh
./setup-inicial.sh
```

---

## 🌐 **ACESSOS**

### **URLs Principais:**
- **📱 Sistema**: `http://**********`
- **📱 Direto**: `http://**********:5000`
- **📊 Portainer**: `http://**********:9000`
- **🔀 Traefik**: `http://**********:8080`

### **Credenciais Padrão:**
- **Email**: `<EMAIL>`
- **Senha**: `Admin@Local123`

---

## 🔧 **COMANDOS ÚTEIS**

### **Status dos Serviços:**
```bash
docker stack ls
docker service ls
docker service ps dossie_dossie-app
```

### **Logs:**
```bash
# Logs da aplicação
docker service logs dossie_dossie-app --tail 50

# Logs do PostgreSQL
docker service logs postgres_postgres --tail 20

# Logs do Traefik
docker service logs traefik_traefik --tail 20
```

### **Backup:**
```bash
# Backup do banco
docker exec $(docker ps -q -f name=postgres_postgres) pg_dump -U dossie dossie_escola > backup_$(date +%Y%m%d).sql

# Backup dos uploads
docker run --rm -v dossie_app_uploads:/data -v /opt/dossie-app/backups:/backup alpine tar czf /backup/uploads_$(date +%Y%m%d).tar.gz -C /data .
```

### **Restart dos Serviços:**
```bash
# Restart da aplicação
docker service update --force dossie_dossie-app

# Restart do PostgreSQL
docker service update --force postgres_postgres

# Restart do Traefik
docker service update --force traefik_traefik
```

### **Escalar Aplicação:**
```bash
# Aumentar réplicas
docker service scale dossie_dossie-app=3

# Diminuir réplicas
docker service scale dossie_dossie-app=1
```

---

## 🛠️ **TROUBLESHOOTING**

### **Problema: Aplicação não responde**
```bash
# Verificar status
docker service ps dossie_dossie-app

# Verificar logs
docker service logs dossie_dossie-app --tail 100

# Restart forçado
docker service update --force dossie_dossie-app
```

### **Problema: Banco não conecta**
```bash
# Verificar PostgreSQL
docker service ps postgres_postgres

# Testar conexão
docker exec -it $(docker ps -q -f name=postgres_postgres) psql -U dossie -d dossie_escola -c "SELECT 1;"

# Restart PostgreSQL
docker service update --force postgres_postgres
```

### **Problema: Traefik não roteia**
```bash
# Verificar Traefik
docker service logs traefik_traefik --tail 50

# Verificar redes
docker network ls

# Restart Traefik
docker service update --force traefik_traefik
```

### **Problema: Portainer não abre**
```bash
# Verificar Portainer
docker service ps portainer_portainer

# Acesso direto
curl -I http://**********:9000

# Restart Portainer
docker service update --force portainer_portainer
```

---

## 📊 **MONITORAMENTO**

### **Recursos do Sistema:**
```bash
# CPU e Memória
htop

# Espaço em disco
df -h

# Uso do Docker
docker system df

# Stats dos containers
docker stats
```

### **Health Checks:**
```bash
# Status de saúde dos serviços
docker service ls

# Detalhes de um serviço
docker service inspect dossie_dossie-app --pretty

# Logs de health check
docker service logs dossie_dossie-app | grep health
```

---

## 🔄 **ATUALIZAÇÕES**

### **Atualizar Aplicação:**
```bash
cd /opt/dossie-app

# Atualizar código (se usando Git)
cd app && git pull && cd ..

# Rebuild e deploy
cd app && docker build -t dossie-app:latest . && cd ..
docker service update --image dossie-app:latest dossie_dossie-app
```

### **Atualizar Imagens Docker:**
```bash
# Atualizar Traefik
docker service update --image traefik:v3.0 traefik_traefik

# Atualizar Portainer
docker service update --image portainer/portainer-ce:latest portainer_portainer

# Atualizar PostgreSQL (CUIDADO!)
docker service update --image postgres:15-alpine postgres_postgres
```

---

## 🔒 **SEGURANÇA**

### **Firewall:**
```bash
# Status do firewall
sudo ufw status

# Permitir nova porta
sudo ufw allow 3000/tcp

# Bloquear IP
sudo ufw deny from *************
```

### **Logs de Segurança:**
```bash
# Logs de autenticação
sudo tail -f /var/log/auth.log

# Logs do sistema
sudo journalctl -f

# Logs do Docker
sudo journalctl -u docker -f
```

---

## 📱 **ACESSO REMOTO**

### **Da Rede Local:**
Qualquer computador da rede `********/24` pode acessar:
- `http://**********` - Sistema principal
- `http://**********:9000` - Portainer
- `http://**********:8080` - Traefik

### **Configurar Acesso Externo (Opcional):**
```bash
# Configurar port forwarding no roteador
# Porta 80 -> **********:80
# Porta 443 -> **********:443

# Ou usar ngrok para teste
ngrok http **********:80
```

---

## 📋 **CHECKLIST DE VERIFICAÇÃO**

### **✅ Após Instalação:**
- [ ] Docker Swarm ativo
- [ ] Redes criadas (traefik-public, app-network)
- [ ] Traefik respondendo em :8080
- [ ] Portainer respondendo em :9000
- [ ] PostgreSQL rodando
- [ ] Aplicação respondendo em :80 e :5000

### **✅ Após Deploy:**
- [ ] Todas as stacks ativas
- [ ] Todos os serviços rodando
- [ ] Health checks passando
- [ ] Logs sem erros críticos
- [ ] Login funcionando
- [ ] Criação de dossiê funcionando

### **✅ Manutenção Regular:**
- [ ] Backup do banco (diário)
- [ ] Backup dos uploads (semanal)
- [ ] Verificar logs (diário)
- [ ] Verificar espaço em disco (semanal)
- [ ] Atualizar sistema (mensal)

---

## 🆘 **SUPORTE**

### **Comandos de Emergência:**
```bash
# Parar tudo
docker stack rm dossie traefik portainer postgres

# Limpar sistema
docker system prune -f

# Reiniciar Docker
sudo systemctl restart docker

# Reiniciar servidor
sudo reboot
```

### **Backup de Emergência:**
```bash
# Backup completo
tar czf emergency_backup_$(date +%Y%m%d).tar.gz /opt/dossie-app
```

### **Restaurar Sistema:**
```bash
# Restaurar do backup
tar xzf emergency_backup_YYYYMMDD.tar.gz -C /

# Recriar redes
docker network create --driver overlay traefik-public
docker network create --driver overlay app-network

# Redeploy
cd /opt/dossie-app && ./deploy-local.sh
```

---

## 🎯 **RESUMO**

**✅ Sistema funcionando em:** `http://**********`
**✅ Gerenciamento via:** `http://**********:9000`
**✅ Monitoramento via:** `http://**********:8080`

**🔑 Login:** `<EMAIL>` / `Admin@Local123`

**📁 Diretório:** `/opt/dossie-app`
**🐳 Stack:** Docker Swarm + Traefik + Portainer + PostgreSQL

**🎉 Sistema pronto para produção na rede local!** 🚀
