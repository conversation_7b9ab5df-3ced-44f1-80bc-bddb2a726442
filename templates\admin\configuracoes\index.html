{% extends "admin/base.html" %}

{% block title %}Configurações do Sistema{% endblock %}
{% block header %}Configurações do Sistema{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4><i class="fas fa-cogs me-2"></i>Configurações do Sistema</h4>
                <p class="text-muted">G<PERSON><PERSON><PERSON> to<PERSON> as configurações da aplicação</p>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                    <i class="fas fa-download me-2"></i>Exportar
                </button>
                <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="fas fa-upload me-2"></i>Importar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Estatísticas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-cogs fa-2x mb-2 text-primary"></i>
                <h3>{{ categorias|length }}</h3>
                <p class="mb-0">Categorias</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-sliders-h fa-2x mb-2 text-success"></i>
                <h3>
                    {% set total_configs = 0 %}
                    {% for categoria, dados in categorias.items() %}
                        {% set total_configs = total_configs + dados.configuracoes|length %}
                    {% endfor %}
                    {{ total_configs }}
                </h3>
                <p class="mb-0">Configurações</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-2x mb-2 text-warning"></i>
                <h3>{{ get_config('security.audit_logging.enabled', True) and 'ON' or 'OFF' }}</h3>
                <p class="mb-0">Auditoria</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-database fa-2x mb-2 text-info"></i>
                <h3>{{ get_config('backup.auto_backup_enabled', False) and 'ON' or 'OFF' }}</h3>
                <p class="mb-0">Backup Auto</p>
            </div>
        </div>
    </div>
</div>

<!-- Categorias de Configuração -->
<div class="row">
    {% for categoria_key, categoria_dados in categorias.items() %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    {% if categoria_key == 'security' %}
                        <i class="fas fa-shield-alt me-2 text-danger"></i>
                    {% elif categoria_key == 'dossie' %}
                        <i class="fas fa-folder me-2 text-primary"></i>
                    {% elif categoria_key == 'escola' %}
                        <i class="fas fa-school me-2 text-success"></i>
                    {% elif categoria_key == 'system' %}
                        <i class="fas fa-server me-2 text-info"></i>
                    {% elif categoria_key == 'integration' %}
                        <i class="fas fa-plug me-2 text-warning"></i>
                    {% elif categoria_key == 'notification' %}
                        <i class="fas fa-bell me-2 text-secondary"></i>
                    {% else %}
                        <i class="fas fa-cog me-2"></i>
                    {% endif %}
                    {{ categoria_dados.nome }}
                </h5>
                <span class="badge bg-primary">{{ categoria_dados.configuracoes|length }}</span>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for chave, config_data in categoria_dados.configuracoes.items() %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">{{ config_data.config.nome_exibicao }}</h6>
                            <p class="mb-1 text-muted small">{{ config_data.config.descricao or 'Sem descrição' }}</p>
                            <small class="text-muted">
                                <code>{{ chave }}</code>
                                {% if config_data.config.obrigatoria %}
                                    <span class="badge bg-danger ms-1">Obrigatória</span>
                                {% endif %}
                                {% if config_data.config.requer_reinicializacao %}
                                    <span class="badge bg-warning ms-1">Requer Restart</span>
                                {% endif %}
                            </small>
                        </div>
                        <div class="text-end">
                            <div class="mb-1">
                                {% if config_data.config.tipo == 'boolean' %}
                                    <span class="badge bg-{{ 'success' if config_data.valor else 'secondary' }}">
                                        {{ 'Ativo' if config_data.valor else 'Inativo' }}
                                    </span>
                                {% else %}
                                    <code class="small">{{ config_data.valor|string|truncate(20) }}</code>
                                {% endif %}
                            </div>
                            {% if config_data.config.editavel %}
                            <a href="{{ url_for('configuracao.editar', config_id=config_data.config.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-3">
                    <a href="{{ url_for('configuracao.categoria', categoria=categoria_key) }}" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-cogs me-2"></i>Gerenciar {{ categoria_dados.nome }}
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Modal de Exportação -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Exportar Configurações</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label class="form-label">Escopo da Exportação</label>
                        <select class="form-select" name="escola_id">
                            <option value="">Todas as configurações</option>
                            <option value="{{ session.escola_id }}">Apenas desta escola</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        As configurações serão exportadas em formato JSON para backup ou migração.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="exportarConfiguracoes()">
                    <i class="fas fa-download me-2"></i>Exportar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Importação -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importar Configurações</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('configuracao.importar') }}" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Arquivo de Configurações</label>
                        <input type="file" class="form-control" name="arquivo" accept=".json" required>
                        <div class="form-text">Selecione um arquivo JSON exportado anteriormente.</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="sobrescrever" value="true">
                            <label class="form-check-label">
                                Sobrescrever configurações existentes
                            </label>
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Atenção:</strong> A importação pode alterar configurações críticas do sistema.
                        Certifique-se de ter um backup antes de prosseguir.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Importar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function exportarConfiguracoes() {
    const form = document.getElementById('exportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    window.location.href = '{{ url_for("configuracao.exportar") }}?' + params.toString();
    
    // Fechar modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
    modal.hide();
}
</script>
{% endblock %}
