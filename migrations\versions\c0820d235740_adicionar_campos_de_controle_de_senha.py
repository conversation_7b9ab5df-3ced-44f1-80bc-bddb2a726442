"""Adicionar campos de controle de senha

Revision ID: c0820d235740
Revises: 59b09fe3235e
Create Date: 2025-06-11 00:19:20.794517

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c0820d235740'
down_revision = '59b09fe3235e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('usuarios', schema=None) as batch_op:
        batch_op.add_column(sa.Column('data_alteracao_senha', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('senha_expira_em', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('usuarios', schema=None) as batch_op:
        batch_op.drop_column('senha_expira_em')
        batch_op.drop_column('data_alteracao_senha')

    # ### end Alembic commands ###
