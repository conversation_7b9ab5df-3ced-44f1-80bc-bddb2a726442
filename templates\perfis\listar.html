{% extends "base.html" %}

{% block title %}Perfis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-shield me-2"></i>
                Gestão de Perfis
            </h1>
            <a href="{{ url_for('perfil.novo') }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                Novo Perfil
            </a>
        </div>
    </div>
</div>

<!-- Filtros e Busca -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-10">
                        <label for="search" class="form-label">Buscar</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="Nome do perfil...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-success">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Perfis -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if perfis.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Nome do Perfil</th>
                                    <th>Descrição</th>
                                    <th>Usuários</th>
                                    <th>Tipo</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for perfil in perfis.items %}
                                <tr>
                                    <td>
                                        <code>{{ perfil.id_perfil }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ perfil.perfil }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ perfil.descricao }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('perfil.usuarios', id=perfil.id_perfil) }}" class="badge bg-info text-decoration-none">
                                            {{ perfil.usuarios | length }} usuário(s)
                                        </a>
                                    </td>
                                    <td>
                                        {% set perfis_sistema = ['Administrador Geral', 'Administrador da Escola', 'Operador', 'Consulta'] %}
                                        {% if perfil.perfil in perfis_sistema %}
                                            <span class="badge bg-warning">Sistema</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Personalizado</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('perfil.ver', id=perfil.id_perfil) }}" class="btn btn-sm btn-outline-info" title="Ver detalhes">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('perfil.editar', id=perfil.id_perfil) }}" class="btn btn-sm btn-outline-warning" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('perfil.usuarios', id=perfil.id_perfil) }}" class="btn btn-sm btn-outline-primary" title="Ver usuários">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            {% if perfil.perfil not in perfis_sistema %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="Excluir"
                                                    onclick="confirmarExclusao({{ perfil.id_perfil }}, '{{ perfil.perfil }}', '{{ url_for('perfil.excluir', id=perfil.id_perfil) }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginação -->
                    {% if perfis.pages > 1 %}
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination justify-content-center">
                            {% if perfis.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('perfil.listar', page=perfis.prev_num, search=search) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in perfis.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != perfis.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('perfil.listar', page=page_num, search=search) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if perfis.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('perfil.listar', page=perfis.next_num, search=search) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum perfil encontrado</h5>
                        {% if search %}
                            <p class="text-muted">Tente ajustar os filtros de busca</p>
                            <a href="{{ url_for('perfil.listar') }}" class="btn btn-outline-success">
                                <i class="fas fa-times me-2"></i>
                                Limpar filtros
                            </a>
                        {% else %}
                            <p class="text-muted">Comece cadastrando o primeiro perfil personalizado</p>
                            <a href="{{ url_for('perfil.novo') }}" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                Cadastrar Perfil
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Estatísticas -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ perfis.total }}</h4>
                <p class="mb-0">Total de Perfis</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <h4>{{ perfis.items | selectattr('perfil', 'in', ['Administrador Geral', 'Administrador da Escola', 'Operador', 'Consulta']) | list | length }}</h4>
                <p class="mb-0">Perfis do Sistema</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h4>{{ perfis.items | rejectattr('perfil', 'in', ['Administrador Geral', 'Administrador da Escola', 'Operador', 'Consulta']) | list | length }}</h4>
                <p class="mb-0">Perfis Personalizados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ (perfis.items | map(attribute='usuarios') | map('length') | sum) if perfis.items else 0 }}</h4>
                <p class="mb-0">Usuários Vinculados</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmação -->
<div class="modal fade" id="modalExcluir" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Exclusão</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o perfil <strong id="perfilNome"></strong>?</p>
                <p class="text-danger"><small>Esta ação não pode ser desfeita e afetará todos os usuários vinculados.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="formExcluir" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Excluir</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmarExclusao(id, nome, url) {
        document.getElementById('perfilNome').textContent = nome;
        document.getElementById('formExcluir').action = url;
        new bootstrap.Modal(document.getElementById('modalExcluir')).show();
    }
</script>
{% endblock %}
