version: '3.8'

services:
  dossie-app:
    build: /var/www/dossie_escolar
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=*********************************************************/dossie_escola
      - SECRET_KEY=${SECRET_KEY}
      - SERVER_NAME=**********
      - UPLOAD_FOLDER=/app/static/uploads
      - MAX_CONTENT_LENGTH=16777216
    ports:
      - "5000:5000"
    volumes:
      - app_uploads:/app/static/uploads
      - app_logs:/app/logs
      - /var/www/dossie_escolar:/app
    networks:
      - traefik-public
      - app-network
    depends_on:
      - postgres
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 5s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.http.routers.dossie.rule=Host(`**********`)
        - traefik.http.routers.dossie.entrypoints=web
        - traefik.http.services.dossie.loadbalancer.server.port=5000
        - traefik.http.middlewares.dossie-headers.headers.customrequestheaders.X-Forwarded-Proto=http
        - traefik.http.middlewares.dossie-headers.headers.customrequestheaders.X-Forwarded-For=$$remote_addr
        - traefik.http.routers.dossie.middlewares=dossie-headers
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  app_uploads:
  app_logs:

networks:
  traefik-public:
    external: true
  app-network:
    external: true
