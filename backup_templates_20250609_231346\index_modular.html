<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Controle de Dossiê Escolar - Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="jumbotron bg-primary text-white text-center py-5 rounded mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-graduation-cap me-3"></i>
                        Sistema de Controle de Dossiê Escolar
                    </h1>
                    <p class="lead">Arquitetura Modular - Conforme CLAUDE.md</p>
                    <hr class="my-4 bg-white">
                    <p>Sistema SaaS Multi-tenant para 15 escolas municipais</p>
                    <a class="btn btn-light btn-lg" href="{{ url_for('login') }}">
                        <i class="fas fa-sign-in-alt me-2"></i>Acessar Sistema
                    </a>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-cubes text-primary me-2"></i>
                                    Aplicações Modulares
                                </h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i><strong>AUTH</strong> - Autenticação e Segurança</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>CORE</strong> - Entidades auxiliares</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>ESCOLAS</strong> - Gestão de escolas</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>USUÁRIOS</strong> - Gestão de usuários</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>DOSSIÊS</strong> - Gestão de dossiês</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>MOVIMENTAÇÕES</strong> - Controle de movimentações</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>SOLICITANTES</strong> - Gestão de solicitantes</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>LOGS</strong> - Logs e auditoria</li>
                                    <li><i class="fas fa-check text-success me-2"></i><strong>RELATÓRIOS</strong> - Relatórios do sistema</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-shield-alt text-success me-2"></i>
                                    Funcionalidades Implementadas
                                </h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Sistema Multi-tenant</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Controle de acesso granular</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Auditoria completa</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Conformidade LGPD</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gestão de escolas</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gestão de usuários</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gestão de dossiês</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Controle de movimentações</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Relatórios avançados</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Arquitetura Modular
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>
                                    Este sistema foi desenvolvido com arquitetura modular conforme especificação do CLAUDE.md.
                                    Cada entidade possui sua própria aplicação com models, routes e templates específicos.
                                </p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>Estrutura:</h6>
                                        <ul class="list-unstyled small">
                                            <li><code>apps/auth/</code> - Autenticação</li>
                                            <li><code>apps/core/</code> - Entidades auxiliares</li>
                                            <li><code>apps/escolas/</code> - Gestão de escolas</li>
                                            <li><code>apps/usuarios/</code> - Gestão de usuários</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Cada aplicação possui:</h6>
                                        <ul class="list-unstyled small">
                                            <li><i class="fas fa-file me-1"></i><code>models.py</code> - Modelos</li>
                                            <li><i class="fas fa-file me-1"></i><code>routes.py</code> - Rotas</li>
                                            <li><i class="fas fa-file me-1"></i><code>utils.py</code> - Utilitários</li>
                                            <li><i class="fas fa-folder me-1"></i><code>templates/</code> - Templates</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Benefícios:</h6>
                                        <ul class="list-unstyled small">
                                            <li><i class="fas fa-check text-success me-1"></i>Código organizado</li>
                                            <li><i class="fas fa-check text-success me-1"></i>Fácil manutenção</li>
                                            <li><i class="fas fa-check text-success me-1"></i>Escalabilidade</li>
                                            <li><i class="fas fa-check text-success me-1"></i>Reutilização</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
