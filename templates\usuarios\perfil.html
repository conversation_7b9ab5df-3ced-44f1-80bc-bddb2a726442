{% extends "base.html" %}

{% block title %}Meu Perfil{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-user me-2"></i>Meu Perfil</h2>
                    <p class="text-muted">Visualize e edite suas informações pessoais</p>
                </div>
                <div>
                    <a href="{{ url_for('usuario.editar_perfil') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Editar Perfil
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Informações Pessoais -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-circle me-2"></i>Informações Pessoais</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Nome Completo</label>
                            <div class="form-control-plaintext">{{ usuario.nome }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email</label>
                            <div class="form-control-plaintext">{{ usuario.email }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">CPF</label>
                            <div class="form-control-plaintext">{{ usuario.cpf|format_cpf }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Telefone</label>
                            <div class="form-control-plaintext">{{ usuario.telefone or 'Não informado' }}</div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Endereço</label>
                            <div class="form-control-plaintext">{{ usuario.endereco or 'Não informado' }}</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Data de Nascimento</label>
                            <div class="form-control-plaintext">
                                {% if usuario.data_nascimento %}
                                    {{ usuario.data_nascimento.strftime('%d/%m/%Y') }}
                                    <small class="text-muted">({{ usuario.data_nascimento|age }} anos)</small>
                                {% else %}
                                    Não informado
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Situação</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-{{ 'success' if usuario.situacao == 'ativo' else 'danger' }}">
                                    {{ usuario.situacao.title() }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Foto do Usuário -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-camera me-2"></i>Foto do Perfil</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img id="userPhoto"
                             src="{{ usuario.get_foto_url() }}"
                             alt="Foto do usuário"
                             class="rounded-circle img-thumbnail"
                             style="width: 150px; height: 150px; object-fit: cover;">
                    </div>

                    <div class="mb-3">
                        <input type="file"
                               id="photoInput"
                               accept="image/*"
                               style="display: none;"
                               onchange="uploadPhoto()">

                        <button type="button"
                                class="btn btn-primary btn-sm me-2"
                                onclick="document.getElementById('photoInput').click()">
                            <i class="fas fa-upload me-1"></i>Alterar Foto
                        </button>

                        {% if usuario.has_foto() %}
                        <button type="button"
                                class="btn btn-outline-danger btn-sm"
                                onclick="removePhoto()">
                            <i class="fas fa-trash me-1"></i>Remover
                        </button>
                        {% endif %}
                    </div>

                    <small class="text-muted">
                        Formatos aceitos: PNG, JPG, JPEG, GIF, WEBP<br>
                        Tamanho máximo: 5MB
                    </small>
                </div>
            </div>

            <!-- Informações do Sistema -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-cog me-2"></i>Informações do Sistema</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted small">Perfil</label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-primary">{{ usuario.perfil_obj.perfil if usuario.perfil_obj else 'Não definido' }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted small">Escola</label>
                        <div class="form-control-plaintext">{{ usuario.escola.nome if usuario.escola else 'Não definido' }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted small">Data de Cadastro</label>
                        <div class="form-control-plaintext">
                            {% if usuario.data_cadastro %}
                                {{ usuario.data_cadastro.strftime('%d/%m/%Y às %H:%M') }}
                            {% else %}
                                Não informado
                            {% endif %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted small">Último Login</label>
                        <div class="form-control-plaintext">
                            {% if usuario.ultimo_login %}
                                {{ usuario.ultimo_login.strftime('%d/%m/%Y às %H:%M') }}
                            {% else %}
                                Nunca
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ações Rápidas -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Ações Rápidas</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('usuario.editar_perfil') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Editar Informações
                        </a>
                        <a href="{{ url_for('usuario.alterar_senha') }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-key me-2"></i>Alterar Senha
                        </a>
                        <a href="{{ url_for('configuracao.index') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-cog me-2"></i>Configurações
                        </a>
                        <hr class="my-2">
                        <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-sign-out-alt me-2"></i>Sair do Sistema
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas de Atividade -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Atividade Recente</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary">-</h4>
                                <small class="text-muted">Dossiês Criados</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">-</h4>
                                <small class="text-muted">Movimentações</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-info">-</h4>
                                <small class="text-muted">Ações Registradas</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">
                                {% if usuario.data_cadastro %}
                                    {{ usuario.data_cadastro.strftime('%d/%m/%Y') }}
                                {% else %}
                                    -
                                {% endif %}
                            </h4>
                            <small class="text-muted">Data de Cadastro</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control-plaintext {
    padding: 0.375rem 0;
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: #212529;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 1px solid #dee2e6 !important;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }
}

/* Estilos para foto do usuário */
.img-thumbnail {
    border: 3px solid #dee2e6;
    transition: all 0.3s ease;
}

.img-thumbnail:hover {
    border-color: #007bff;
    transform: scale(1.05);
}

#userPhoto {
    cursor: pointer;
}
</style>

<script>
// Função para upload de foto
function uploadPhoto() {
    const fileInput = document.getElementById('photoInput');
    const file = fileInput.files[0];

    if (!file) {
        return;
    }

    // Validar tamanho do arquivo (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('Arquivo muito grande! O tamanho máximo é 5MB.');
        return;
    }

    // Validar tipo do arquivo
    const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('Tipo de arquivo não permitido! Use PNG, JPG, JPEG, GIF ou WEBP.');
        return;
    }

    // Criar FormData
    const formData = new FormData();
    formData.append('foto', file);

    // Mostrar loading
    const userPhoto = document.getElementById('userPhoto');
    const originalSrc = userPhoto.src;
    userPhoto.style.opacity = '0.5';

    // Fazer upload
    fetch('/api/foto/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar foto
            userPhoto.src = data.foto_url + '?t=' + new Date().getTime(); // Cache bust
            userPhoto.style.opacity = '1';

            // Atualizar foto na barra de navegação
            const navPhoto = document.querySelector('#userDropdown img');
            if (navPhoto) {
                navPhoto.src = data.foto_url + '?t=' + new Date().getTime();
            }

            // Mostrar mensagem de sucesso
            showAlert('success', data.message);

            // Recarregar página após 2 segundos para atualizar botões
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            userPhoto.style.opacity = '1';
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        userPhoto.style.opacity = '1';
        showAlert('error', 'Erro ao fazer upload da foto: ' + error.message);
    });
}

// Função para remover foto
function removePhoto() {
    if (!confirm('Tem certeza que deseja remover sua foto?')) {
        return;
    }

    const userPhoto = document.getElementById('userPhoto');
    userPhoto.style.opacity = '0.5';

    fetch('/api/foto/remove', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar foto
            userPhoto.src = data.foto_url + '?t=' + new Date().getTime();
            userPhoto.style.opacity = '1';

            // Atualizar foto na barra de navegação
            const navPhoto = document.querySelector('#userDropdown img');
            if (navPhoto) {
                navPhoto.src = data.foto_url + '?t=' + new Date().getTime();
            }

            // Mostrar mensagem de sucesso
            showAlert('success', data.message);

            // Recarregar página após 2 segundos para atualizar botões
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            userPhoto.style.opacity = '1';
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        userPhoto.style.opacity = '1';
        showAlert('error', 'Erro ao remover foto: ' + error.message);
    });
}

// Função para mostrar alertas
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Inserir alerta no topo da página
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Remover alerta após 5 segundos
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// Permitir clique na foto para alterar
document.getElementById('userPhoto').addEventListener('click', function() {
    document.getElementById('photoInput').click();
});
</script>
{% endblock %}
