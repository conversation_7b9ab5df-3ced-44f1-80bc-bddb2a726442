{% extends "base_completo.html" %}

{% block title %}Login - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow-lg">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Acesso ao Sistema
                </h4>
                <small>Sistema de Controle de Dossiê Escolar</small>
            </div>
            <div class="card-body p-4">
                <form method="POST" id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            Email
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required 
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="senha" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            Senha
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="senha" name="senha" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="lembrarMe">
                        <label class="form-check-label" for="lembrarMe">
                            Lembrar-me neste dispositivo
                        </label>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Entrar no Sistema
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#recuperarSenhaModal">
                            <i class="fas fa-key me-1"></i>
                            Esqueci minha senha
                        </a>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light">
                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-muted mb-2">Perfis de Acesso:</h6>
                        <div class="row">
                            <div class="col-4">
                                <small class="badge bg-danger">Admin Geral</small>
                            </div>
                            <div class="col-4">
                                <small class="badge bg-warning text-dark">Admin Escola</small>
                            </div>
                            <div class="col-4">
                                <small class="badge bg-info">Operacional</small>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Usuário de teste: <EMAIL> / admin123
                    </small>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-3">
            <a href="{{ url_for('index') }}" class="text-decoration-none">
                <i class="fas fa-arrow-left me-1"></i>
                Voltar ao início
            </a>
        </div>
        
        <!-- Informações de Segurança -->
        <div class="card mt-4 border-warning">
            <div class="card-body">
                <h6 class="card-title text-warning">
                    <i class="fas fa-shield-alt me-2"></i>
                    Segurança do Sistema
                </h6>
                <ul class="list-unstyled small mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Máximo de 5 tentativas de login</li>
                    <li><i class="fas fa-check text-success me-2"></i>Bloqueio automático por 30 minutos</li>
                    <li><i class="fas fa-check text-success me-2"></i>Auditoria completa de acessos</li>
                    <li><i class="fas fa-check text-success me-2"></i>Conformidade com LGPD</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Modal Recuperar Senha -->
<div class="modal fade" id="recuperarSenhaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>
                    Recuperar Senha
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="recuperarSenhaForm">
                    <div class="mb-3">
                        <label for="emailRecuperacao" class="form-label">Email cadastrado:</label>
                        <input type="email" class="form-control" id="emailRecuperacao" required>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Enviaremos um link para redefinição de senha no seu email.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="enviarRecuperacao()">
                    <i class="fas fa-paper-plane me-2"></i>
                    Enviar Link
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#senha');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Validação do formulário
    $('#loginForm').on('submit', function(e) {
        const email = $('#email').val().trim();
        const senha = $('#senha').val().trim();
        
        if (!email || !senha) {
            e.preventDefault();
            alert('Por favor, preencha todos os campos.');
            return false;
        }
        
        // Mostrar loading
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Entrando...');
    });
    
    // Auto-focus no campo email
    $('#email').focus();
});

function enviarRecuperacao() {
    const email = $('#emailRecuperacao').val().trim();
    
    if (!email) {
        alert('Por favor, informe seu email.');
        return;
    }
    
    // Simular envio (implementar backend)
    alert('Link de recuperação enviado para: ' + email);
    $('#recuperarSenhaModal').modal('hide');
}

// Detectar tentativas de força bruta
let tentativasLogin = 0;
$('#loginForm').on('submit', function() {
    tentativasLogin++;
    if (tentativasLogin > 3) {
        console.warn('Múltiplas tentativas de login detectadas');
    }
});
</script>
{% endblock %}
