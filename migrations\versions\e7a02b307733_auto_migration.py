"""Auto migration

Revision ID: e7a02b307733
Revises: 
Create Date: 2025-06-08 17:51:14.829194

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e7a02b307733'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('movimentacoes', schema=None) as batch_op:
        batch_op.add_column(sa.Column('data_conclusao', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('movimentacoes', schema=None) as batch_op:
        batch_op.drop_column('data_conclusao')

    # ### end Alembic commands ###
