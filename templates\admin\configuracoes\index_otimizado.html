{% extends "base.html" %}

{% block title %}Configurações do Sistema{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">Admin</a></li>
<li class="breadcrumb-item active">Configurações</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-1">
                    <i class="fas fa-cog me-2"></i>
                    Configurações do Sistema
                </h1>
                <p class="text-muted mb-0"><PERSON><PERSON><PERSON><PERSON> as configurações principais do sistema</p>
            </div>
            <div>
                <span class="badge bg-success">
                    <i class="fas fa-check me-1"></i>Siste<PERSON>o
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Configurações Rápidas -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Segurança
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">Configurações de Segurança</div>
                        <div class="mt-2">
                            <a href="#security" class="btn btn-primary btn-sm">
                                <i class="fas fa-shield-alt me-1"></i>Configurar
                            </a>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Sistema
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">Configurações do Sistema</div>
                        <div class="mt-2">
                            <a href="#system" class="btn btn-success btn-sm">
                                <i class="fas fa-server me-1"></i>Configurar
                            </a>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Interface
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">Interface do Usuário</div>
                        <div class="mt-2">
                            <a href="#interface" class="btn btn-info btn-sm">
                                <i class="fas fa-palette me-1"></i>Configurar
                            </a>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-palette fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Backup
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">Backup e Restauração</div>
                        <div class="mt-2">
                            <a href="{{ url_for('admin.backup') }}" class="btn btn-warning btn-sm">
                                <i class="fas fa-download me-1"></i>Backup
                            </a>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-download fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configurações Principais -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>Configurações Principais
                </h6>
            </div>
            <div class="card-body">
                
                <!-- Seção Segurança -->
                <div id="security" class="mb-4">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-shield-alt me-2"></i>Segurança
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Tempo de Sessão (minutos)</label>
                                <input type="number" class="form-control" value="30" min="5" max="480">
                                <small class="form-text text-muted">Tempo limite para sessões inativas</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Máximo de Tentativas de Login</label>
                                <input type="number" class="form-control" value="5" min="3" max="10">
                                <small class="form-text text-muted">Tentativas antes de bloquear usuário</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seção Sistema -->
                <div id="system" class="mb-4">
                    <h5 class="text-success mb-3">
                        <i class="fas fa-server me-2"></i>Sistema
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Modo de Debug</label>
                                <select class="form-control">
                                    <option value="false">Desabilitado</option>
                                    <option value="true">Habilitado</option>
                                </select>
                                <small class="form-text text-muted">Ativar apenas em desenvolvimento</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Tamanho Máximo de Upload (MB)</label>
                                <input type="number" class="form-control" value="16" min="1" max="100">
                                <small class="form-text text-muted">Tamanho máximo para arquivos</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seção Interface -->
                <div id="interface" class="mb-4">
                    <h5 class="text-info mb-3">
                        <i class="fas fa-palette me-2"></i>Interface
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Tema Padrão</label>
                                <select class="form-control">
                                    <option value="light">Claro</option>
                                    <option value="dark">Escuro</option>
                                    <option value="auto">Automático</option>
                                </select>
                                <small class="form-text text-muted">Tema da interface do usuário</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Itens por Página</label>
                                <select class="form-control">
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <small class="form-text text-muted">Número de itens nas listagens</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Botões de Ação -->
                <div class="text-end">
                    <button type="button" class="btn btn-secondary me-2">
                        <i class="fas fa-undo me-1"></i>Restaurar Padrões
                    </button>
                    <button type="button" class="btn btn-primary" onclick="salvarConfiguracoes()">
                        <i class="fas fa-save me-1"></i>Salvar Configurações
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Acesso Rápido -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-rocket me-2"></i>Acesso Rápido
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('admin.system_info') }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-info-circle me-2"></i>Informações do Sistema
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-success btn-block">
                            <i class="fas fa-file-alt me-2"></i>Logs do Sistema
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('admin.backup') }}" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-download me-2"></i>Backup
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-arrow-left me-2"></i>Voltar ao Admin
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function salvarConfiguracoes() {
    // Simular salvamento rápido
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';
    btn.disabled = true;
    
    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Salvo!';
        btn.className = 'btn btn-success';
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.className = 'btn btn-primary';
            btn.disabled = false;
        }, 2000);
    }, 1000);
}

// Scroll suave para seções
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.btn-block {
    width: 100%;
}

.card {
    border: 1px solid #e3e6f0;
}
</style>
{% endblock %}
