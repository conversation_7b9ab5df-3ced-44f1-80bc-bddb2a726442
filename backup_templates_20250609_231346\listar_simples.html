{% extends "base.html" %}

{% block title %}Escolas - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">Escolas</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-school me-2"></i>
                Gestão de Escolas
            </h1>
            <a href="/escolas/nova" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Nova Escola
            </a>
        </div>
    </div>
</div>

<!-- Filtros de Busca -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <label for="search" class="form-label">Buscar</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="Nome, CNPJ ou INEP da escola">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>Buscar
                        </button>
                        {% if search %}
                        <a href="/escolas/" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Limpar
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Escolas -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Lista de Escolas
                    <span class="badge bg-primary ms-2">{{ escolas.total }} escola(s)</span>
                </h5>
            </div>
            <div class="card-body">
                {% if escolas.items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Nome</th>
                                <th>UF</th>
                                <th>CNPJ</th>
                                <th>INEP</th>
                                <th>Diretor</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for escola in escolas.items %}
                            <tr>
                                <td>
                                    <strong>{{ escola.nome }}</strong>
                                    {% if escola.email %}
                                    <br><small class="text-muted">{{ escola.email }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ escola.uf }}</span>
                                </td>
                                <td>
                                    <code>{{ escola.cnpj or '-' }}</code>
                                </td>
                                <td>
                                    <code>{{ escola.inep or '-' }}</code>
                                </td>
                                <td>{{ escola.diretor or '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/escolas/ver/{{ escola.id }}" 
                                           class="btn btn-sm btn-outline-info" title="Ver detalhes">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/escolas/editar/{{ escola.id }}" 
                                           class="btn btn-sm btn-outline-warning" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                title="Excluir" onclick="confirmarExclusao({{ escola.id }}, '{{ escola.nome }}', '/escolas/excluir/{{ escola.id }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {% if escolas.pages > 1 %}
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination justify-content-center">
                        {% if escolas.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="/escolas/?page={{ escolas.prev_num }}&search={{ search }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in escolas.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != escolas.page %}
                                <li class="page-item">
                                    <a class="page-link" href="/escolas/?page={{ page_num }}&search={{ search }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if escolas.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="/escolas/?page={{ escolas.next_num }}&search={{ search }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-school fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhuma escola encontrada</h5>
                    {% if search %}
                        <p class="text-muted">Tente ajustar os filtros de busca</p>
                        <a href="/escolas/" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>Limpar filtros
                        </a>
                    {% else %}
                        <p class="text-muted">Comece cadastrando a primeira escola</p>
                        <a href="/escolas/nova" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Cadastrar Escola
                        </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
