<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sistema de Controle de Dossiê Escolar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>
                Dossiê Escolar - Modular
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ usuario.nome }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>Sair
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard - {{ usuario.perfil_obj.nome }}
                </h1>
            </div>
        </div>

        <!-- Cards de Estatísticas -->
        <div class="row mb-4">
            {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ stats.total_escolas }}</h4>
                        <p class="mb-0">Escolas</p>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ stats.total_usuarios }}</h4>
                        <p class="mb-0">Usuários</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ stats.total_dossies }}</h4>
                        <p class="mb-0">Dossiês</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>{{ stats.total_movimentacoes }}</h4>
                        <p class="mb-0">Movimentações</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu de Aplicações -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-th me-2"></i>
                            Aplicações do Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
                            <div class="col-md-3 mb-3">
                                <a href="/escolas/" class="btn btn-outline-primary w-100 h-100">
                                    <i class="fas fa-school fa-2x d-block mb-2"></i>
                                    <strong>Escolas</strong>
                                    <br><small>Gestão de escolas</small>
                                </a>
                            </div>
                            {% endif %}
                            
                            <div class="col-md-3 mb-3">
                                <a href="/usuarios/" class="btn btn-outline-success w-100 h-100">
                                    <i class="fas fa-users fa-2x d-block mb-2"></i>
                                    <strong>Usuários</strong>
                                    <br><small>Gestão de usuários</small>
                                </a>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <a href="/dossies/" class="btn btn-outline-info w-100 h-100">
                                    <i class="fas fa-folder fa-2x d-block mb-2"></i>
                                    <strong>Dossiês</strong>
                                    <br><small>Gestão de dossiês</small>
                                </a>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <a href="/movimentacoes/" class="btn btn-outline-warning w-100 h-100">
                                    <i class="fas fa-exchange-alt fa-2x d-block mb-2"></i>
                                    <strong>Movimentações</strong>
                                    <br><small>Controle de movimentações</small>
                                </a>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <a href="/solicitantes/" class="btn btn-outline-secondary w-100 h-100">
                                    <i class="fas fa-user-friends fa-2x d-block mb-2"></i>
                                    <strong>Solicitantes</strong>
                                    <br><small>Gestão de solicitantes</small>
                                </a>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <a href="/relatorios/" class="btn btn-outline-dark w-100 h-100">
                                    <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                                    <strong>Relatórios</strong>
                                    <br><small>Relatórios do sistema</small>
                                </a>
                            </div>
                            
                            {% if usuario.perfil_obj.nome in ['Administrador Geral', 'Administrador da Escola'] %}
                            <div class="col-md-3 mb-3">
                                <a href="/logs/auditoria" class="btn btn-outline-danger w-100 h-100">
                                    <i class="fas fa-history fa-2x d-block mb-2"></i>
                                    <strong>Logs</strong>
                                    <br><small>Auditoria e logs</small>
                                </a>
                            </div>
                            {% endif %}
                            
                            <div class="col-md-3 mb-3">
                                <a href="/core/configuracoes/{{ usuario.escola_id }}" class="btn btn-outline-info w-100 h-100">
                                    <i class="fas fa-cog fa-2x d-block mb-2"></i>
                                    <strong>Configurações</strong>
                                    <br><small>Configurações do sistema</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações da Escola -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informações
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Usuário:</strong> {{ usuario.nome }}</p>
                                <p><strong>Perfil:</strong> {{ usuario.perfil_obj.nome }}</p>
                                <p><strong>Email:</strong> {{ usuario.email }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Escola:</strong> {{ usuario.escola.nome }}</p>
                                <p><strong>Último Acesso:</strong> 
                                    {% if usuario.ultimo_acesso %}
                                        {{ usuario.ultimo_acesso.strftime('%d/%m/%Y às %H:%M') }}
                                    {% else %}
                                        Primeiro acesso
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
